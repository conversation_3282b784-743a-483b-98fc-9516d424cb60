// Dashboard 相关路由配置 - 嵌套路由结构
export const dashboardRoutes = {
  path: '/dashboard',
  name: 'Dashboard',
  component: () => import('@/views/DashboardView.vue'),
  meta: {
    title: '工作台',
    requiresAuth: true, // 需要登录
    icon: 'House',
  },
  children: [
    {
      // 默认子路由 - 访问 /dashboard 时显示工作台内容
      path: '',
      name: 'DashboardHome',
      component: () => import('@/views/DashboardHome.vue'),
      meta: {
        title: '工作台',
        icon: 'House',
      },
    },
    {
      path: 'messages',
      name: 'Messages',
      component: () => import('@/views/ChatRoom/index.vue'),
      meta: {
        title: '消息中心',
        icon: 'ChatLineRound',
      },
    },
    {
      path: 'contacts',
      name: 'Contacts',
      component: () => import('@/views/Account/index.vue'),
      meta: {
        title: '联系人',
        icon: 'User',
      },
    },
    {
      path: 'shop',
      name: 'Shop',
      component: () => import('@/views/Shop/index.vue'),
      meta: {
        title: '商城',
        icon: 'Shop',
      },
    },
    {
      path: 'workshop',
      name: 'Workshop',
      component: () => import('@/views/ToolsBox/index.vue'),
      meta: {
        title: '创意工坊',
        icon: 'Grid',
      },
    },
    {
      path: 'blog',
      name: 'Blog',
      component: () => import('@/views/Blog/index.vue'),
      meta: {
        title: '个人博客',
        icon: 'Edit',
      },
    },
  ],
}

// 默认重定向到工作台
export const defaultRedirect = {
  path: '/',
  redirect: '/dashboard',
}
