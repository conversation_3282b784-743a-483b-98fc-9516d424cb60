<template>
  <div class="home-page">
    <!-- 顶部导航栏 -->
    <el-affix :offset="0">
      <el-header class="header">
        <div class="container">
          <div class="nav-wrapper">
            <!-- Logo -->
            <div class="logo">
              <h1>EliteHub</h1>
            </div>

            <!-- 桌面端导航 -->
            <nav class="desktop-nav">
              <el-menu mode="horizontal" :ellipsis="false" class="main-nav" router>
                <el-menu-item index="#features">功能</el-menu-item>
                <el-menu-item index="#messages">消息</el-menu-item>
                <el-menu-item index="#shop">商城</el-menu-item>
                <el-menu-item index="#workshop">工坊</el-menu-item>
              </el-menu>
            </nav>

            <!-- 桌面端用户操作 -->
            <div class="desktop-actions">
              <el-button type="text" class="login-btn">登录</el-button>
              <el-button type="primary" class="register-btn">注册</el-button>
            </div>

            <!-- 移动端菜单按钮 -->
            <div class="mobile-menu-btn" @click="toggleMobileMenu">
              <div class="hamburger-icon" :class="{ 'hamburger-open': mobileMenuOpen }">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
          </div>

          <!-- 移动端菜单 -->
          <div class="mobile-menu" :class="{ 'mobile-menu-open': mobileMenuOpen }">
            <div class="mobile-nav-items">
              <a href="#features" @click="closeMobileMenu">功能</a>
              <a href="#messages" @click="closeMobileMenu">消息</a>
              <a href="#shop" @click="closeMobileMenu">商城</a>
              <a href="#workshop" @click="closeMobileMenu">工坊</a>
            </div>
            <div class="mobile-actions">
              <el-button type="text" class="mobile-login-btn" @click="closeMobileMenu"
                >登录</el-button
              >
              <el-button type="primary" class="mobile-register-btn" @click="closeMobileMenu"
                >注册</el-button
              >
            </div>
          </div>
        </div>
      </el-header>
    </el-affix>

    <!-- 英雄区域 -->
    <div class="hero">
      <div class="hero-background"></div>
      <div class="container">
        <div class="hero-content">
          <h1 class="hero-title">连接世界，创造价值</h1>
          <p class="hero-subtitle">EliteHub - 新一代社交商务平台，让沟通更高效，让商业更简单</p>
          <div class="hero-actions">
            <el-button size="large" type="primary" class="cta-primary">
              立即体验
              <el-icon class="ml-2"><ArrowRight /></el-icon>
            </el-button>
            <el-button size="large" plain class="cta-secondary" style="margin: 0"
              >了解更多</el-button
            >
          </div>
          <div class="hero-stats">
            <div class="stat-item">
              <div class="stat-number">10K+</div>
              <div class="stat-label">活跃用户</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">500+</div>
              <div class="stat-label">商家入驻</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">99.9%</div>
              <div class="stat-label">服务可用性</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 功能区域 -->
    <div id="features" class="section features-section">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">核心功能</h2>
          <p class="section-subtitle">为您提供全方位的数字化解决方案</p>
        </div>
        <div class="features-grid">
          <div
            v-for="(feature, index) in features"
            :key="index"
            class="feature-card"
            :class="`z-${index + 1}`"
          >
            <div class="feature-icon">
              <el-icon :size="32">
                <component :is="feature.icon" />
              </el-icon>
            </div>
            <h3 class="feature-title">{{ feature.title }}</h3>
            <p class="feature-description">{{ feature.description }}</p>
            <div class="feature-arrow">
              <el-icon><ArrowRight /></el-icon>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 消息展示 -->
    <div id="messages" class="section messages-section">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">智能通讯</h2>
          <p class="section-subtitle">让每一次沟通都更有价值</p>
        </div>
        <div class="messages-showcase">
          <div class="showcase-content">
            <div class="feature-highlights">
              <div class="highlight-item">
                <div class="highlight-icon">
                  <el-icon><Message /></el-icon>
                </div>
                <div class="highlight-content">
                  <h4>实时消息</h4>
                  <p>毫秒级消息传递，支持文字、图片、语音、视频</p>
                </div>
              </div>
              <div class="highlight-item">
                <div class="highlight-icon">
                  <el-icon><VideoPlay /></el-icon>
                </div>
                <div class="highlight-content">
                  <h4>高清通话</h4>
                  <p>4K视频通话，AI降噪，让距离不再是问题</p>
                </div>
              </div>
              <div class="highlight-item">
                <div class="highlight-icon">
                  <el-icon><ChatLineRound /></el-icon>
                </div>
                <div class="highlight-content">
                  <h4>群组协作</h4>
                  <p>智能群组管理，文件共享，提升团队效率</p>
                </div>
              </div>
            </div>
          </div>
          <div class="messages-preview">
            <div class="chat-mockup">
              <div class="chat-header">
                <div class="chat-avatar"></div>
                <div class="chat-info">
                  <div class="chat-name">产品团队</div>
                  <div class="chat-status">3人在线</div>
                </div>
              </div>
              <div class="chat-messages">
                <div class="message-item left">
                  <div class="message-content">新功能开发进度如何？</div>
                  <div class="message-time">10:30</div>
                </div>
                <div class="message-item right">
                  <div class="message-content">已完成80%，预计明天上线</div>
                  <div class="message-time">10:32</div>
                </div>
                <div class="message-item left">
                  <div class="message-content">太棒了！👍</div>
                  <div class="message-time">10:33</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 商城展示 -->
    <div id="shop" class="section shop-section">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">智慧商城</h2>
          <p class="section-subtitle">开启您的数字商业之旅</p>
        </div>
        <div class="shop-showcase">
          <div class="shop-features">
            <div class="shop-feature-card" v-for="(item, index) in shopFeatures" :key="index">
              <div class="feature-icon-wrapper" :style="{ backgroundColor: item.color }">
                <el-icon :size="40">
                  <component :is="item.icon" />
                </el-icon>
              </div>
              <div class="feature-content">
                <h3>{{ item.title }}</h3>
                <p>{{ item.description }}</p>
                <el-button type="primary" text class="learn-more">
                  了解更多 <el-icon class="ml-1"><ArrowRight /></el-icon>
                </el-button>
              </div>
            </div>
          </div>
          <div class="shop-stats">
            <div class="stat-card">
              <div class="stat-icon">
                <el-icon><Shop /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">500+</div>
                <div class="stat-label">入驻商家</div>
              </div>
            </div>
            <div class="stat-card">
              <div class="stat-icon">
                <el-icon><ShoppingCart /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">10K+</div>
                <div class="stat-label">商品种类</div>
              </div>
            </div>
            <div class="stat-card">
              <div class="stat-icon">
                <el-icon><Check /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">98%</div>
                <div class="stat-label">好评率</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 创意工坊展示 -->
    <div id="workshop" class="section workshop-section">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">创意工坊</h2>
          <p class="section-subtitle">发现无限可能，释放创造潜能</p>
        </div>
        <div class="workshop-showcase">
          <div class="workshop-features">
            <div class="workshop-card">
              <div class="workshop-icon">
                <el-icon :size="48"><Edit /></el-icon>
              </div>
              <h3>个人博客</h3>
              <p>打造专属博客空间，自由装饰页面，分享您的思考与创作</p>
              <div class="workshop-tags">
                <span class="tag">自定义主题</span>
                <span class="tag">富文本编辑</span>
                <span class="tag">多媒体支持</span>
              </div>
            </div>
            <div class="workshop-card">
              <div class="workshop-icon">
                <el-icon :size="48"><Grid /></el-icon>
              </div>
              <h3>实用工具</h3>
              <p>汇聚各种实用小工具，提升工作效率，让生活更便捷</p>
              <div class="workshop-tags">
                <span class="tag">文档转换</span>
                <span class="tag">图片处理</span>
                <span class="tag">数据分析</span>
              </div>
            </div>
            <div class="workshop-card">
              <div class="workshop-icon">
                <el-icon :size="48"><Present /></el-icon>
              </div>
              <h3>趣味功能</h3>
              <p>探索有趣的互动功能，为平台体验增添更多乐趣</p>
              <div class="workshop-tags">
                <span class="tag">小游戏</span>
                <span class="tag">表情包</span>
                <span class="tag">趣味测试</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 号召性动作 -->
    <div class="section cta-section">
      <div class="container">
        <div class="cta-content">
          <div class="cta-text">
            <h2>准备好开始了吗？</h2>
            <p>加入EliteHub，体验下一代社交商务平台</p>
          </div>
          <div class="cta-actions">
            <el-button size="large" type="primary" class="cta-primary">
              免费注册
              <el-icon class="ml-2"><ArrowRight /></el-icon>
            </el-button>
            <el-button size="large" plain class="cta-secondary" style="margin: 0"
              >联系我们</el-button
            >
          </div>
        </div>
      </div>
    </div>

    <!-- 页脚 -->
    <footer class="footer">
      <div class="footer-container">
        <!-- 主要内容区 -->
        <div class="footer-main">
          <div class="footer-brand">
            <h3 class="brand-title">EliteHub</h3>
            <p class="brand-desc">连接世界，创造价值</p>
            <div class="social-icons">
              <el-button circle size="small" class="social-btn">
                <el-icon><Message /></el-icon>
              </el-button>
              <el-button circle size="small" class="social-btn">
                <el-icon><VideoPlay /></el-icon>
              </el-button>
              <el-button circle size="small" class="social-btn">
                <el-icon><Shop /></el-icon>
              </el-button>
            </div>
          </div>

          <div class="footer-links">
            <div class="link-column">
              <h4>产品</h4>
              <a href="#features">核心功能</a>
              <a href="#messages">智能通讯</a>
              <a href="#shop">智慧商城</a>
              <a href="#workshop">创意工坊</a>
            </div>
            <div class="link-column">
              <h4>公司</h4>
              <a href="#">关于我们</a>
              <a href="#">加入我们</a>
              <a href="#">联系我们</a>
            </div>
            <div class="link-column">
              <h4>支持</h4>
              <a href="#">帮助中心</a>
              <a href="#">开发者</a>
              <a href="#">反馈</a>
            </div>
          </div>
        </div>

        <!-- 底部信息 -->
        <div class="footer-bottom">
          <div class="footer-info">
            <p>&copy; {{ new Date().getFullYear() }} EliteHub. 保留所有权利</p>
            <p>京ICP备12345678号-1</p>
          </div>
          <div class="footer-badge">
            <span class="version-tag">v1.0.0</span>
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import {
  ArrowRight,
  Check,
  Message,
  VideoPlay,
  Shop,
  ShoppingCart,
  Promotion,
  ChatLineRound,
  UserFilled,
  ChatDotRound,
  Edit,
  Grid,
  Present,
} from '@element-plus/icons-vue'

// 移动端菜单状态
const mobileMenuOpen = ref(false)

// 切换移动端菜单
const toggleMobileMenu = () => {
  mobileMenuOpen.value = !mobileMenuOpen.value
}

// 关闭移动端菜单
const closeMobileMenu = () => {
  mobileMenuOpen.value = false
}

// 功能区数据
const features = ref([
  {
    icon: ChatLineRound,
    title: '即时通讯',
    description: '与好友实时聊天，支持文字、图片、语音等多媒体消息',
  },
  {
    icon: UserFilled,
    title: '联系人管理',
    description: '轻松管理您的联系人，查找并添加新朋友',
  },
  {
    icon: ChatDotRound,
    title: '群组聊天',
    description: '创建和管理群组，与多人同时交流分享',
  },
  {
    icon: Shop,
    title: '在线商城',
    description: '开设自己的商店，或在平台上购买心仪商品',
  },
  {
    icon: Edit,
    title: '个人博客',
    description: '创建专属博客空间，自由装饰和分享您的精彩内容',
  },
  {
    icon: Grid,
    title: '创意工坊',
    description: '汇聚各种实用工具和趣味功能，激发您的创造力',
  },
])

// 商城功能数据
const shopFeatures = ref([
  {
    title: '开设店铺',
    description: '零门槛开店，轻松管理商品和订单',
    icon: Shop,
    color: '#f0f9eb',
  },
  {
    title: '便捷购物',
    description: '多种支付方式，安全快捷的购物体验',
    icon: ShoppingCart,
    color: '#ecf5ff',
  },
  {
    title: '营销工具',
    description: '丰富的营销功能，帮助店铺提升销量',
    icon: Promotion,
    color: '#f5f7fa',
  },
])
</script>

<style scoped lang="less">
@import '../assets/styles/common.less';

.home-page {
  min-height: 100vh;
  background: linear-gradient(180deg, #fafbfc 0%, #ffffff 100%);
}

// 顶部导航
.header {
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  padding: 0;
  position: relative;
  z-index: 1000;

  .container {
    padding: 0 @spacing-lg;
  }

  .nav-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    // height: 70px;
  }

  .logo {
    h1 {
      background: linear-gradient(135deg, @primary-color, @accent-color);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      font-size: 32px;
      font-weight: @font-weight-bold;
      margin: 0;
    }
  }

  // 桌面端导航
  .desktop-nav {
    .main-nav {
      border: none;
      background: transparent;

      :deep(.el-menu-item) {
        padding: 0 24px;
        margin: 0 4px;
        border-radius: 8px;
        transition: all 0.3s ease;

        &:hover {
          background-color: rgba(58, 123, 213, 0.1);
          color: @primary-color;
        }
      }
    }
  }

  // 桌面端用户操作
  .desktop-actions {
    display: flex;
    gap: @spacing-sm;

    .login-btn {
      color: @text-color;
      font-weight: @font-weight-medium;

      &:hover {
        color: @primary-color;
      }
    }

    .register-btn {
      background: linear-gradient(135deg, @primary-color, @accent-color);
      border: none;
      border-radius: 25px;
      padding: 8px 24px;
      font-weight: @font-weight-medium;
      box-shadow: 0 4px 12px rgba(58, 123, 213, 0.3);

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(58, 123, 213, 0.4);
      }
    }
  }

  // 移动端菜单按钮
  .mobile-menu-btn {
    display: none;
    cursor: pointer;
    padding: 12px;
    border-radius: 8px;
    transition: all 0.3s ease;

    &:hover {
      background-color: rgba(0, 0, 0, 0.05);
    }

    .hamburger-icon {
      width: 24px;
      height: 18px;
      position: relative;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      span {
        display: block;
        height: 2px;
        width: 100%;
        background-color: @text-color;
        border-radius: 1px;
        transition: all 0.3s ease;
        transform-origin: center;
      }

      &.hamburger-open {
        span:nth-child(1) {
          transform: rotate(45deg) translate(6px, 6px);
        }

        span:nth-child(2) {
          opacity: 0;
        }

        span:nth-child(3) {
          transform: rotate(-45deg) translate(6px, -6px);
        }
      }
    }

    &:hover .hamburger-icon span {
      background-color: @primary-color;
    }
  }

  // 移动端菜单
  .mobile-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;

    &.mobile-menu-open {
      transform: translateY(0);
      opacity: 1;
      visibility: visible;
    }

    .mobile-nav-items {
      padding: @spacing-lg;
      border-bottom: 1px solid rgba(0, 0, 0, 0.05);

      a {
        display: block;
        padding: @spacing-md 0;
        color: @text-color;
        text-decoration: none;
        font-size: @font-size-lg;
        font-weight: @font-weight-medium;
        transition: color 0.3s ease;

        &:hover {
          color: @primary-color;
        }
      }
    }

    .mobile-actions {
      padding: @spacing-lg;
      display: flex;
      gap: @spacing-md;

      .mobile-login-btn {
        flex: 1;
        color: @text-color;
        font-weight: @font-weight-medium;
      }

      .mobile-register-btn {
        flex: 1;
        background: linear-gradient(135deg, @primary-color, @accent-color);
        border: none;
        border-radius: 25px;
        font-weight: @font-weight-medium;
      }
    }
  }
}

// 英雄区域
.hero {
  position: relative;
  padding: 120px 0 100px;
  overflow: hidden;

  .hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      rgba(58, 123, 213, 0.1) 0%,
      rgba(110, 69, 226, 0.1) 50%,
      rgba(0, 210, 255, 0.1) 100%
    );

    &::before {
      content: '';
      position: absolute;
      top: -50%;
      right: -20%;
      width: 600px;
      height: 600px;
      background: radial-gradient(circle, rgba(58, 123, 213, 0.1) 0%, transparent 70%);
      border-radius: 50%;
    }

    &::after {
      content: '';
      position: absolute;
      bottom: -30%;
      left: -10%;
      width: 400px;
      height: 400px;
      background: radial-gradient(circle, rgba(110, 69, 226, 0.1) 0%, transparent 70%);
      border-radius: 50%;
    }
  }

  .hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    max-width: 800px;
    margin: 0 auto;

    .hero-title {
      font-size: 56px;
      font-weight: @font-weight-bold;
      line-height: 1.2;
      margin-bottom: @spacing-lg;
      background: linear-gradient(135deg, @primary-color, @accent-color);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .hero-subtitle {
      font-size: @font-size-xl;
      color: @text-color-secondary;
      margin-bottom: @spacing-xxl;
      line-height: 1.6;
    }

    .hero-actions {
      display: flex;
      justify-content: center;
      gap: @spacing-lg;
      margin-bottom: @spacing-xxl;

      .cta-primary {
        background: linear-gradient(135deg, @primary-color, @accent-color);
        border: none;
        border-radius: 30px;
        padding: 16px 32px;
        font-size: @font-size-lg;
        font-weight: @font-weight-medium;
        box-shadow: 0 8px 24px rgba(58, 123, 213, 0.3);

        &:hover {
          transform: translateY(-3px);
          box-shadow: 0 12px 32px rgba(58, 123, 213, 0.4);
        }

        .ml-2 {
          margin-left: 8px;
        }
      }

      .cta-secondary {
        border: 2px solid @primary-color;
        color: @primary-color;
        border-radius: 30px;
        padding: 14px 32px;
        font-size: @font-size-lg;
        font-weight: @font-weight-medium;

        &:hover {
          background-color: @primary-color;
          color: @white;
          transform: translateY(-3px);
        }
      }
    }

    .hero-stats {
      display: flex;
      justify-content: center;
      gap: @spacing-xxl;

      .stat-item {
        text-align: center;

        .stat-number {
          font-size: 32px;
          font-weight: @font-weight-bold;
          color: @primary-color;
          margin-bottom: @spacing-xs;
        }

        .stat-label {
          font-size: @font-size-sm;
          color: @text-color-secondary;
        }
      }
    }
  }
}

// 通用部分样式
.section {
  padding: 100px 0;

  .section-header {
    text-align: center;
    margin-bottom: @spacing-xxl;

    .section-title {
      font-size: 42px;
      font-weight: @font-weight-bold;
      margin-bottom: @spacing-md;
      background: linear-gradient(135deg, @primary-color, @accent-color);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .section-subtitle {
      font-size: @font-size-lg;
      color: @text-color-secondary;
      max-width: 600px;
      margin: 0 auto;
    }
  }
}

// 功能区域
.features-section {
  .features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: @spacing-xl;

    .feature-card {
      position: relative;
      background: @white;
      border-radius: 20px;
      padding: @spacing-xl;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      transition: all 0.3s ease;
      border: 1px solid rgba(0, 0, 0, 0.05);

      &:hover {
        transform: translateY(-8px);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);

        .feature-arrow {
          opacity: 1;
          transform: translateX(5px);
        }
      }

      .feature-icon {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, @primary-color, @accent-color);
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: @spacing-lg;

        .el-icon {
          color: @white;
        }
      }

      .feature-title {
        font-size: @font-size-xl;
        font-weight: @font-weight-bold;
        color: @text-color;
        margin-bottom: @spacing-md;
      }

      .feature-description {
        color: @text-color-secondary;
        line-height: 1.6;
        margin-bottom: @spacing-lg;
      }

      .feature-arrow {
        position: absolute;
        bottom: @spacing-lg;
        right: @spacing-lg;
        opacity: 0;
        transition: all 0.3s ease;
        color: @primary-color;
      }
    }
  }
}

// 消息展示区
.messages-section {
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);

  .messages-showcase {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: @spacing-xxl;
    align-items: center;

    .showcase-content {
      .feature-highlights {
        .highlight-item {
          display: flex;
          align-items: flex-start;
          margin-bottom: @spacing-xl;

          .highlight-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, @primary-color, @accent-color);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: @spacing-lg;
            flex-shrink: 0;

            .el-icon {
              color: @white;
              font-size: 20px;
            }
          }

          .highlight-content {
            h4 {
              font-size: @font-size-lg;
              font-weight: @font-weight-bold;
              color: @text-color;
              margin-bottom: @spacing-xs;
            }

            p {
              color: @text-color-secondary;
              line-height: 1.6;
            }
          }
        }
      }
    }

    .messages-preview {
      .chat-mockup {
        background: @white;
        border-radius: 20px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        overflow: hidden;

        .chat-header {
          padding: @spacing-lg;
          border-bottom: 1px solid @border-color-light;
          display: flex;
          align-items: center;

          .chat-avatar {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, @primary-color, @accent-color);
            border-radius: 50%;
            margin-right: @spacing-md;
          }

          .chat-info {
            .chat-name {
              font-weight: @font-weight-medium;
              color: @text-color;
            }

            .chat-status {
              font-size: @font-size-sm;
              color: @text-color-secondary;
            }
          }
        }

        .chat-messages {
          padding: @spacing-lg;
          height: 300px;

          .message-item {
            margin-bottom: @spacing-lg;

            &.left {
              .message-content {
                background: @bg-color-dark;
                color: @text-color;
                border-radius: 18px 18px 18px 4px;
              }
            }

            &.right {
              text-align: right;

              .message-content {
                background: linear-gradient(135deg, @primary-color, @accent-color);
                color: @white;
                border-radius: 18px 18px 4px 18px;
                display: inline-block;
              }
            }

            .message-content {
              padding: @spacing-md @spacing-lg;
              max-width: 80%;
              display: inline-block;
            }

            .message-time {
              font-size: @font-size-xs;
              color: @text-color-light;
              margin-top: @spacing-xs;
            }
          }
        }
      }
    }
  }
}

// 商城展示区
.shop-section {
  .shop-showcase {
    .shop-features {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: @spacing-xl;
      margin-bottom: @spacing-xxl;

      .shop-feature-card {
        background: @white;
        border-radius: 20px;
        padding: @spacing-xl;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
        border: 1px solid rgba(0, 0, 0, 0.05);

        &:hover {
          transform: translateY(-5px);
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
        }

        .feature-icon-wrapper {
          width: 80px;
          height: 80px;
          border-radius: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: @spacing-lg;

          .el-icon {
            color: @primary-color;
          }
        }

        .feature-content {
          h3 {
            font-size: @font-size-xl;
            font-weight: @font-weight-bold;
            color: @text-color;
            margin-bottom: @spacing-md;
          }

          p {
            color: @text-color-secondary;
            line-height: 1.6;
            margin-bottom: @spacing-lg;
          }

          .learn-more {
            font-weight: @font-weight-medium;

            .ml-1 {
              margin-left: 4px;
            }
          }
        }
      }
    }

    .shop-stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: @spacing-lg;

      .stat-card {
        background: linear-gradient(135deg, @primary-color, @accent-color);
        border-radius: 16px;
        padding: @spacing-xl;
        color: @white;
        display: flex;
        align-items: center;

        .stat-icon {
          width: 50px;
          height: 50px;
          background: rgba(255, 255, 255, 0.2);
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: @spacing-lg;

          .el-icon {
            font-size: 24px;
          }
        }

        .stat-info {
          .stat-number {
            font-size: 28px;
            font-weight: @font-weight-bold;
            margin-bottom: @spacing-xs;
          }

          .stat-label {
            font-size: @font-size-sm;
            opacity: 0.9;
          }
        }
      }
    }
  }
}

// 创意工坊区域
.workshop-section {
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);

  .workshop-showcase {
    .workshop-features {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
      gap: @spacing-xl;

      .workshop-card {
        background: @white;
        border-radius: 20px;
        padding: @spacing-xl;
        text-align: center;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
        border: 1px solid rgba(0, 0, 0, 0.05);

        &:hover {
          transform: translateY(-8px);
          box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        .workshop-icon {
          width: 80px;
          height: 80px;
          background: linear-gradient(135deg, @primary-color, @accent-color);
          border-radius: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 auto @spacing-lg;

          .el-icon {
            color: @white;
          }
        }

        h3 {
          font-size: @font-size-xl;
          font-weight: @font-weight-bold;
          color: @text-color;
          margin-bottom: @spacing-md;
        }

        p {
          color: @text-color-secondary;
          line-height: 1.6;
          margin-bottom: @spacing-lg;
        }

        .workshop-tags {
          display: flex;
          flex-wrap: wrap;
          gap: @spacing-sm;
          justify-content: center;

          .tag {
            background: linear-gradient(135deg, @primary-color, @accent-color);
            color: @white;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: @font-size-xs;
            font-weight: @font-weight-medium;
          }
        }
      }
    }
  }
}

// CTA区域
.cta-section {
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);

  .cta-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: @white;
    border-radius: 24px;
    padding: @spacing-xxl;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

    .cta-text {
      h2 {
        font-size: 36px;
        font-weight: @font-weight-bold;
        color: @text-color;
        margin-bottom: @spacing-md;
      }

      p {
        font-size: @font-size-lg;
        color: @text-color-secondary;
      }
    }

    .cta-actions {
      display: flex;
      gap: @spacing-lg;

      .cta-primary {
        background: linear-gradient(135deg, @primary-color, @accent-color);
        border: none;
        border-radius: 25px;
        padding: 16px 32px;
        font-size: @font-size-lg;
        font-weight: @font-weight-medium;
        box-shadow: 0 6px 20px rgba(58, 123, 213, 0.3);

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 28px rgba(58, 123, 213, 0.4);
        }

        .ml-2 {
          margin-left: 8px;
        }
      }

      .cta-secondary {
        border: 2px solid @primary-color;
        color: @primary-color;
        border-radius: 25px;
        padding: 14px 32px;
        font-size: @font-size-lg;
        font-weight: @font-weight-medium;

        &:hover {
          background-color: @primary-color;
          color: @white;
          transform: translateY(-2px);
        }
      }
    }
  }
}

// 页脚
.footer {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  color: @white;
  padding: 60px 0 20px;

  .footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  .footer-main {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 60px;
    margin-bottom: 40px;

    .footer-brand {
      .brand-title {
        font-size: 28px;
        font-weight: @font-weight-bold;
        background: linear-gradient(135deg, @primary-color, @accent-color);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin: 0 0 12px 0;
      }

      .brand-desc {
        color: rgba(255, 255, 255, 0.8);
        font-size: 16px;
        margin: 0 0 24px 0;
        line-height: 1.6;
      }

      .social-icons {
        display: flex;
        gap: 12px;

        .social-btn {
          background: rgba(255, 255, 255, 0.1);
          border: 1px solid rgba(255, 255, 255, 0.2);
          color: @white;

          &:hover {
            background: linear-gradient(135deg, @primary-color, @accent-color);
            border-color: transparent;
            transform: translateY(-2px);
          }
        }
      }
    }

    .footer-links {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 40px;

      .link-column {
        h4 {
          color: @white;
          font-size: 16px;
          font-weight: @font-weight-bold;
          margin: 0 0 16px 0;
        }

        a {
          display: block;
          color: rgba(255, 255, 255, 0.7);
          text-decoration: none;
          font-size: 14px;
          margin-bottom: 8px;
          transition: color 0.3s ease;

          &:hover {
            color: @white;
          }
        }
      }
    }
  }

  .footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .footer-info {
      p {
        color: rgba(255, 255, 255, 0.6);
        font-size: 14px;
        margin: 0 0 4px 0;

        &:last-child {
          font-size: 12px;
          margin: 0;
        }
      }
    }

    .footer-badge {
      .version-tag {
        background: linear-gradient(135deg, @primary-color, @accent-color);
        color: @white;
        padding: 4px 12px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: @font-weight-medium;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  // 导航栏响应式
  .header {
    .desktop-nav,
    .desktop-actions {
      display: none;
    }

    .mobile-menu-btn {
      display: block;
    }
  }

  // Hero区域响应式
  .hero {
    padding: 80px 0 60px;

    .hero-content {
      .hero-title {
        font-size: 36px;
      }

      .hero-actions {
        flex-direction: column;
        align-items: center;
        gap: @spacing-md;
      }

      .hero-stats {
        flex-direction: column;
        gap: @spacing-lg;
      }
    }
  }

  // Footer响应式
  .footer {
    padding: 40px 0 20px;

    .footer-container {
      padding: 0 16px;
    }

    .footer-main {
      grid-template-columns: 1fr;
      gap: 40px;

      .footer-brand {
        text-align: center;
      }

      .footer-links {
        grid-template-columns: repeat(2, 1fr);
        gap: 30px;
      }
    }

    .footer-bottom {
      flex-direction: column;
      gap: 16px;
      text-align: center;
    }
  }

  // 功能卡片响应式
  .features-grid {
    grid-template-columns: 1fr !important;
    gap: @spacing-lg !important;
  }

  // 消息展示响应式
  .messages-showcase {
    grid-template-columns: 1fr !important;
    gap: @spacing-xl !important;
  }

  // 创意工坊响应式
  .workshop-features {
    grid-template-columns: 1fr !important;
    gap: @spacing-lg !important;
  }

  // CTA区域响应式
  .cta-content {
    flex-direction: column !important;
    text-align: center;
    gap: @spacing-xl;

    .cta-actions {
      flex-direction: column;
      width: 100%;

      .el-button {
        width: 100%;
      }
    }
  }
}

@media (max-width: 480px) {
  .header {
    .container {
      padding: 0 @spacing-md;
    }

    .logo h1 {
      font-size: 24px;
    }
  }

  .footer {
    .footer-main {
      .footer-links {
        grid-template-columns: 1fr;
        gap: 24px;
      }
    }
  }
}

// 大屏幕优化
@media (min-width: 769px) {
  .mobile-menu-btn,
  .mobile-menu {
    display: none !important;
  }
}

// 工具类
.ml-1 {
  margin-left: 4px;
}

.ml-2 {
  margin-left: 8px;
}

// 全局动画
* {
  scroll-behavior: smooth;
}

// 按钮悬停效果增强
.el-button {
  transition: all 0.3s ease;
}

// 卡片悬停效果
.el-card {
  transition: all 0.3s ease;
}

// 渐变文字效果
.gradient-text {
  background: linear-gradient(135deg, @primary-color, @accent-color);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
</style>
