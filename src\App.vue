<script setup>
import { RouterView } from 'vue-router'
import CustomContextMenu from './components/CustomContextMenu.vue'
import { useContextMenu } from './composables/useContextMenu'

// 初始化右键菜单
const { contextMenuRef } = useContextMenu()
</script>

<template>
  <RouterView />
  <CustomContextMenu ref="contextMenuRef" />
</template>

<style>
@import './assets/styles/common.less';
@import './assets/styles/element-variables.less';
</style>
