<template>
  <div class="placeholder-page">
    <div class="page-header">
      <h1>创意工坊</h1>
      <p>汇聚各种实用工具和趣味功能</p>
    </div>

    <div class="coming-soon">
      <el-icon :size="64"><Grid /></el-icon>
      <h2>敬请期待</h2>
      <p>创意工坊正在开发中，将为您带来丰富的工具和功能！</p>
      <el-button type="primary" @click="goBack">返回工作台</el-button>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { Grid } from '@element-plus/icons-vue'

const router = useRouter()

const goBack = () => {
  router.push('/dashboard')
}
</script>

<style scoped lang="less">
@import '../../assets/styles/placeholder.less';
</style>
