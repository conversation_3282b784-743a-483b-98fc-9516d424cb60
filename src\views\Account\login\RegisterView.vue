<template>
  <div class="register-page">
    <div class="register-container">
      <div class="register-card">
        <!-- 头部 -->
        <div class="register-header">
          <h1 class="logo">EliteHub</h1>
          <h2>注册</h2>
          <p>创建您的账户，开启数字化之旅</p>
        </div>

        <!-- 注册表单 -->
        <el-form
          ref="registerFormRef"
          :model="registerForm"
          :rules="registerRules"
          class="register-form"
          size="large"
        >
          <el-form-item prop="username">
            <el-input
              v-model="registerForm.username"
              placeholder="请输入用户名"
              prefix-icon="User"
              clearable
            />
          </el-form-item>

          <el-form-item prop="email">
            <el-input
              v-model="registerForm.email"
              placeholder="请输入邮箱"
              prefix-icon="Message"
              clearable
            />
          </el-form-item>

          <el-form-item prop="password">
            <el-input
              v-model="registerForm.password"
              type="password"
              placeholder="请输入密码"
              prefix-icon="Lock"
              show-password
              clearable
            />
          </el-form-item>

          <el-form-item prop="confirmPassword">
            <el-input
              v-model="registerForm.confirmPassword"
              type="password"
              placeholder="请确认密码"
              prefix-icon="Lock"
              show-password
              clearable
            />
          </el-form-item>

          <div class="form-options">
            <el-checkbox v-model="registerForm.agree">
              我已阅读并同意
              <el-link type="primary" class="terms-link">《用户协议》</el-link>
              和
              <el-link type="primary" class="terms-link">《隐私政策》</el-link>
            </el-checkbox>
          </div>

          <el-button
            type="primary"
            class="register-btn"
            :loading="loading"
            @click="handleRegister"
          >
            注册
          </el-button>
        </el-form>

        <!-- 底部链接 -->
        <div class="register-footer">
          <p>
            已有账户？
            <router-link to="/account/login" class="login-link">立即登录</router-link>
          </p>
          <el-link @click="goHome" class="home-link">
            <el-icon><ArrowLeft /></el-icon>
            返回首页
          </el-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft } from '@element-plus/icons-vue'

const router = useRouter()
const registerFormRef = ref()
const loading = ref(false)

// 注册表单数据
const registerForm = reactive({
  username: '',
  email: '',
  password: '',
  confirmPassword: '',
  agree: false
})

// 确认密码验证
const validateConfirmPassword = (rule, value, callback) => {
  if (value !== registerForm.password) {
    callback(new Error('两次输入的密码不一致'))
  } else {
    callback()
  }
}

// 表单验证规则
const registerRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 2, max: 20, message: '用户名长度在2到20个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    { validator: validateConfirmPassword, trigger: 'blur' }
  ]
}

// 处理注册
const handleRegister = async () => {
  if (!registerFormRef.value) return
  
  try {
    const valid = await registerFormRef.value.validate()
    if (!valid) return

    if (!registerForm.agree) {
      ElMessage.warning('请先同意用户协议和隐私政策')
      return
    }

    loading.value = true
    
    // 模拟注册请求
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success('注册成功！')
    router.push('/account/login')
  } catch (error) {
    console.error('注册失败:', error)
    ElMessage.error('注册失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 返回首页
const goHome = () => {
  router.push('/')
}
</script>

<style scoped lang="less">
@import '../../../assets/styles/common.less';

.register-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: @spacing-lg;
}

.register-container {
  width: 100%;
  max-width: 400px;
}

.register-card {
  background: @white;
  border-radius: 20px;
  padding: @spacing-xxl;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
}

.register-header {
  text-align: center;
  margin-bottom: @spacing-xl;

  .logo {
    font-size: 36px;
    font-weight: @font-weight-bold;
    background: linear-gradient(135deg, @primary-color, @accent-color);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: @spacing-md;
  }

  h2 {
    font-size: 28px;
    font-weight: @font-weight-bold;
    color: @text-color;
    margin-bottom: @spacing-sm;
  }

  p {
    color: @text-color-secondary;
    font-size: @font-size-base;
  }
}

.register-form {
  margin-bottom: @spacing-lg;

  .form-options {
    margin-bottom: @spacing-xl;

    .terms-link {
      font-size: @font-size-sm;
    }
  }

  .register-btn {
    width: 100%;
    height: 48px;
    background: linear-gradient(135deg, @primary-color, @accent-color);
    border: none;
    border-radius: 12px;
    font-size: @font-size-lg;
    font-weight: @font-weight-medium;
    box-shadow: 0 4px 12px rgba(58, 123, 213, 0.3);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(58, 123, 213, 0.4);
    }
  }
}

.register-footer {
  text-align: center;

  p {
    color: @text-color-secondary;
    margin-bottom: @spacing-md;

    .login-link {
      color: @primary-color;
      text-decoration: none;
      font-weight: @font-weight-medium;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .home-link {
    color: @text-color-secondary;
    font-size: @font-size-sm;
    display: inline-flex;
    align-items: center;
    gap: @spacing-xs;

    &:hover {
      color: @primary-color;
    }
  }
}

// 响应式设计
@media (max-width: 480px) {
  .register-page {
    padding: @spacing-md;
  }

  .register-card {
    padding: @spacing-xl;
  }

  .register-header {
    .logo {
      font-size: 28px;
    }

    h2 {
      font-size: 24px;
    }
  }
}
</style>
