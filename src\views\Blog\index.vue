<template>
  <div class="placeholder-page">
    <div class="page-header">
      <h1>个人博客</h1>
      <p>记录生活，分享思考</p>
    </div>

    <div class="coming-soon">
      <el-icon :size="64"><Edit /></el-icon>
      <h2>敬请期待</h2>
      <p>个人博客功能正在开发中，很快您就可以创建和装饰自己的博客了！</p>
      <el-button type="primary" @click="goBack">返回工作台</el-button>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { Edit } from '@element-plus/icons-vue'

const router = useRouter()

const goBack = () => {
  router.push('/dashboard')
}
</script>

<style scoped lang="less">
@import '../../assets/styles/placeholder.less';
</style>
