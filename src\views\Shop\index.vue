<template>
  <div class="placeholder-page">
    <div class="page-header">
      <h1>智慧商城</h1>
      <p>发现好物，享受购物乐趣</p>
    </div>

    <div class="coming-soon">
      <el-icon :size="64"><Shop /></el-icon>
      <h2>敬请期待</h2>
      <p>商城功能正在开发中，很快就会为您带来丰富的商品！</p>
      <el-button type="primary" @click="goBack">返回工作台</el-button>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { Shop } from '@element-plus/icons-vue'

const router = useRouter()

const goBack = () => {
  router.push('/dashboard')
}
</script>

<style scoped lang="less">
@import '../../assets/styles/placeholder.less';
</style>
