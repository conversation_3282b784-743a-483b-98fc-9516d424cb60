<template>
  <div class="placeholder-page">
    <div class="page-header">
      <h1>联系人管理</h1>
      <p>管理您的好友和联系人</p>
    </div>

    <div class="coming-soon">
      <el-icon :size="64"><User /></el-icon>
      <h2>敬请期待</h2>
      <p>联系人管理功能正在开发中，很快就会与您见面！</p>
      <el-button type="primary" @click="goBack">返回工作台</el-button>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { User } from '@element-plus/icons-vue'

const router = useRouter()

const goBack = () => {
  router.push('/dashboard')
}
</script>

<style scoped lang="less">
@import '../../assets/styles/placeholder.less';
</style>
