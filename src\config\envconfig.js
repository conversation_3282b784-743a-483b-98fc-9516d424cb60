const ENV = process.env.NODE_ENV

const config = {
  development: {
    baseURL: 'http://localhost:3000/api',
    timeout: 10000,
  },
  production: {
    baseURL: 'https://api.elitehub.com/api',
    timeout: 15000,
  },
  test: {
    baseURL: 'http://test-api.elitehub.com/api',
    timeout: 10000,
  },
}

export default {
  baseURL: config[ENV]?.baseURL || config.development.baseURL,
  timeout: config[ENV]?.timeout || config.development.timeout,
}
