<template>
  <div class="login-page">
    <div class="login-container">
      <div class="login-card">
        <!-- 头部 -->
        <div class="login-header">
          <h1 class="logo">EliteHub</h1>
          <h2>登录</h2>
          <p>欢迎回来，请登录您的账户</p>
        </div>

        <!-- 登录表单 -->
        <el-form
          ref="loginFormRef"
          :model="loginForm"
          :rules="loginRules"
          class="login-form"
          size="large"
        >
          <el-form-item prop="email">
            <el-input
              v-model="loginForm.email"
              placeholder="请输入邮箱"
              prefix-icon="Message"
              clearable
            />
          </el-form-item>

          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="请输入密码"
              prefix-icon="Lock"
              show-password
              clearable
            />
          </el-form-item>

          <div class="form-options">
            <el-checkbox v-model="loginForm.remember">记住我</el-checkbox>
            <el-link type="primary" class="forgot-link">忘记密码？</el-link>
          </div>

          <el-button
            type="primary"
            class="login-btn"
            :loading="loading"
            @click="handleLogin"
          >
            登录
          </el-button>
        </el-form>

        <!-- 底部链接 -->
        <div class="login-footer">
          <p>
            还没有账户？
            <router-link to="/account/login/register" class="register-link">立即注册</router-link>
          </p>
          <el-link @click="goHome" class="home-link">
            <el-icon><ArrowLeft /></el-icon>
            返回首页
          </el-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft } from '@element-plus/icons-vue'

const router = useRouter()
const loginFormRef = ref()
const loading = ref(false)

// 登录表单数据
const loginForm = reactive({
  email: '',
  password: '',
  remember: false
})

// 表单验证规则
const loginRules = {
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ]
}

// 处理登录
const handleLogin = async () => {
  if (!loginFormRef.value) return
  
  try {
    const valid = await loginFormRef.value.validate()
    if (!valid) return

    loading.value = true
    
    // 模拟登录请求
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    ElMessage.success('登录成功！')
    router.push('/dashboard')
  } catch (error) {
    console.error('登录失败:', error)
    ElMessage.error('登录失败，请检查账号密码')
  } finally {
    loading.value = false
  }
}

// 返回首页
const goHome = () => {
  router.push('/')
}
</script>

<style scoped lang="less">
@import '../../../assets/styles/common.less';

.login-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: @spacing-lg;
}

.login-container {
  width: 100%;
  max-width: 400px;
}

.login-card {
  background: @white;
  border-radius: 20px;
  padding: @spacing-xxl;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
}

.login-header {
  text-align: center;
  margin-bottom: @spacing-xxl;

  .logo {
    font-size: 36px;
    font-weight: @font-weight-bold;
    background: linear-gradient(135deg, @primary-color, @accent-color);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: @spacing-md;
  }

  h2 {
    font-size: 28px;
    font-weight: @font-weight-bold;
    color: @text-color;
    margin-bottom: @spacing-sm;
  }

  p {
    color: @text-color-secondary;
    font-size: @font-size-base;
  }
}

.login-form {
  margin-bottom: @spacing-xl;

  .form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: @spacing-xl;

    .forgot-link {
      font-size: @font-size-sm;
    }
  }

  .login-btn {
    width: 100%;
    height: 48px;
    background: linear-gradient(135deg, @primary-color, @accent-color);
    border: none;
    border-radius: 12px;
    font-size: @font-size-lg;
    font-weight: @font-weight-medium;
    box-shadow: 0 4px 12px rgba(58, 123, 213, 0.3);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(58, 123, 213, 0.4);
    }
  }
}

.login-footer {
  text-align: center;

  p {
    color: @text-color-secondary;
    margin-bottom: @spacing-md;

    .register-link {
      color: @primary-color;
      text-decoration: none;
      font-weight: @font-weight-medium;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .home-link {
    color: @text-color-secondary;
    font-size: @font-size-sm;
    display: inline-flex;
    align-items: center;
    gap: @spacing-xs;

    &:hover {
      color: @primary-color;
    }
  }
}

// 响应式设计
@media (max-width: 480px) {
  .login-page {
    padding: @spacing-md;
  }

  .login-card {
    padding: @spacing-xl;
  }

  .login-header {
    .logo {
      font-size: 28px;
    }

    h2 {
      font-size: 24px;
    }
  }
}
</style>
