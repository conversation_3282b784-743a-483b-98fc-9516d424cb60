// 占位页面通用样式
.placeholder-page {
  padding: 80px;
  text-align: center;
  min-height: 60vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  
  .page-header {
    margin-bottom: 80px;
    
    h1 {
      font-size: 48px;
      color: #1a1a1a;
      margin-bottom: 20px;
      font-weight: 600;
      letter-spacing: -1px;
    }
    
    p {
      color: #6b7280;
      font-size: 18px;
      font-weight: 500;
    }
  }
  
  .coming-soon {
    .el-icon {
      color: #3b82f6;
      margin-bottom: 32px;
    }
    
    h2 {
      font-size: 32px;
      color: #1a1a1a;
      margin-bottom: 20px;
      font-weight: 600;
      letter-spacing: -0.5px;
    }
    
    p {
      color: #6b7280;
      margin-bottom: 40px;
      font-size: 16px;
      line-height: 1.6;
      max-width: 500px;
      margin-left: auto;
      margin-right: auto;
    }

    .el-button {
      height: 48px;
      padding: 0 32px;
      border-radius: 12px;
      font-size: 16px;
      font-weight: 500;
    }
  }
}
