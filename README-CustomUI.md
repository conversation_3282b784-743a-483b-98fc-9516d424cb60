# EliteHub 自定义右键菜单功能说明

## 概述

EliteHub 现在支持自定义右键菜单，为用户提供更加个性化和现代化的交互体验。

## 🎯 功能特性

### 智能右键菜单系统

- **通用菜单**: 复制、粘贴、刷新、返回首页、工作台
- **上下文感知**: 根据右键目标自动显示相应菜单
- **智能定位**: 自动调整位置，确保菜单不超出屏幕边界
- **现代化设计**: 圆角、阴影、毛玻璃效果
- **品牌化**: 带有EliteHub标识的菜单头部
- **动画效果**: 平滑的显示/隐藏动画

### 多种菜单类型

1. **默认通用菜单**: 复制、粘贴、刷新、导航
2. **文本选择菜单**: 复制、剪切、粘贴、全选
3. **图片右键菜单**: 复制图片、保存图片、复制链接
4. **链接右键菜单**: 打开链接、新标签页打开、复制链接、添加书签
5. **文件右键菜单**: 打开、下载、复制文件名、分享、删除
6. **输入框菜单**: 复制、剪切、粘贴、全选、清空
7. **侧边栏菜单**: 刷新、折叠、自定义布局
8. **表格菜单**: 复制行、导出数据、编辑、删除

## 🔧 技术实现

### 文件结构

```
src/
├── components/
│   └── CustomContextMenu.vue   # 自定义右键菜单组件
├── composables/
│   └── useContextMenu.js        # 右键菜单管理逻辑
└── App.vue                      # 主应用入口
```

### 核心组件

#### 1. useContextMenu.js

- 禁用浏览器默认右键菜单
- 管理自定义右键菜单的显示/隐藏
- 处理键盘和鼠标事件

#### 2. CustomContextMenu.vue

- 自定义右键菜单的UI组件
- 包含常用操作和导航功能
- 支持路由跳转和系统操作

## 🎨 样式特点

### 右键菜单样式

- **现代设计**: 12px圆角，毛玻璃背景
- **渐变头部**: 蓝紫色渐变品牌色
- **图标支持**: Element Plus图标系统
- **悬停效果**: 微妙的背景色变化

## 🚀 使用方法

### 自动启用

功能已在App.vue中自动启用，无需额外配置：

```vue
<script setup>
import { useContextMenu } from './composables/useContextMenu'

// 自动初始化
const { contextMenuRef } = useContextMenu()
</script>
```

### 智能菜单识别

系统会自动检测右键目标并显示相应菜单：

```html
<!-- 图片元素 - 显示图片菜单 -->
<img src="image.jpg" alt="图片" />

<!-- 链接元素 - 显示链接菜单 -->
<a href="https://example.com">链接</a>

<!-- 输入框 - 显示输入框菜单 -->
<input type="text" placeholder="输入框" />

<!-- 文件项 - 显示文件菜单 -->
<div class="file-item">文件.pdf</div>

<!-- 侧边栏 - 显示侧边栏菜单 -->
<div class="sidebar">侧边栏内容</div>

<!-- 表格 - 显示表格菜单 -->
<table class="el-table">
  表格内容
</table>
```

### 智能定位系统

- **边界检测**: 自动检测屏幕边界
- **位置调整**: 菜单超出边界时自动调整位置
- **最小边距**: 确保菜单与屏幕边缘保持10px间距

## 🎯 交互体验

### 右键菜单

1. **智能识别**: 根据右键目标自动选择菜单类型
2. **智能定位**: 自动调整位置避免超出屏幕
3. **点击选项**: 执行对应功能并关闭菜单
4. **点击空白**: 关闭菜单
5. **ESC键**: 快速关闭菜单

### 功能特点

- **复制粘贴**: 智能识别选中文本或页面链接
- **剪贴板权限**: 自动处理浏览器剪贴板权限
- **错误处理**: 完善的错误提示和异常处理
- **扩展性**: 预留TODO注释，便于后续功能扩展

## 🔧 自定义配置

### 修改右键菜单

编辑 `src/components/CustomContextMenu.vue`:

```vue
// 添加新的菜单项
<div class="menu-item" @click="handleAction('newAction')">
  <el-icon><YourIcon /></el-icon>
  <span>新功能</span>
</div>
```

### 禁用功能

如需禁用右键菜单，在App.vue中注释对应代码：

```vue
<script setup>
// const { contextMenuRef } = useContextMenu()  // 禁用右键菜单
</script>
```

## 🌟 最佳实践

1. **用户体验**: 提供清晰的视觉反馈
2. **可访问性**: 保持键盘导航支持
3. **兼容性**: 优雅降级到默认右键菜单
4. **品牌一致**: 使用统一的颜色和动画

## 🔮 未来扩展

- 添加右键菜单的上下文感知功能
- 支持触摸设备的手势操作
- 支持主题切换
- 添加更多实用功能

## 🎨 样式特点

### 光标样式

- **流畅动画**: 使用cubic-bezier缓动函数
- **状态反馈**: 不同交互状态有不同视觉效果
- **高性能**: 使用transform和opacity优化动画
- **响应式**: 自动适配不同元素类型

### 右键菜单样式

- **现代设计**: 12px圆角，毛玻璃背景
- **渐变头部**: 蓝紫色渐变品牌色
- **图标支持**: Element Plus图标系统
- **悬停效果**: 微妙的背景色变化

## 🚀 使用方法

### 自动启用

功能已在App.vue中自动启用，无需额外配置：

```vue
<script setup>
import { useCustomCursor } from './composables/useCustomCursor'
import { useContextMenu } from './composables/useContextMenu'

// 自动初始化
useCustomCursor()
const { contextMenuRef } = useContextMenu()
</script>
```

### 自定义光标状态

为元素添加特定类名来触发不同光标状态：

```html
<!-- 可点击元素 -->
<div class="clickable">点击我</div>

<!-- 自动检测的元素 -->
<button>按钮</button>
<a href="#">链接</a>
<input type="text" placeholder="输入框" />
```

### 右键菜单功能

- **刷新页面**: 重新加载当前页面
- **导航控制**: 前进/后退页面
- **快速跳转**: 返回首页/工作台
- **实用工具**: 复制链接、分享页面

## 🎯 交互体验

### 光标交互

1. **普通浏览**: 小蓝点跟随鼠标移动
2. **悬停按钮**: 光标变大并显示圆环
3. **点击操作**: 光标变小变红提供反馈
4. **文本输入**: 光标变为竖线样式

### 右键菜单

1. **右键点击**: 在鼠标位置显示菜单
2. **点击选项**: 执行对应功能并关闭菜单
3. **点击空白**: 关闭菜单
4. **ESC键**: 快速关闭菜单

## 🔧 自定义配置

### 修改光标样式

编辑 `src/assets/styles/cursor.less`:

```less
.custom-cursor {
  width: 12px; // 光标大小
  height: 12px;
  background: #3b82f6; // 光标颜色
  // ... 其他样式
}
```

### 修改右键菜单

编辑 `src/components/CustomContextMenu.vue`:

```vue
// 添加新的菜单项
<div class="menu-item" @click="handleAction('newAction')">
  <el-icon><YourIcon /></el-icon>
  <span>新功能</span>
</div>
```

### 禁用功能

如需禁用某个功能，在App.vue中注释对应代码：

```vue
<script setup>
// useCustomCursor()  // 禁用自定义光标
// const { contextMenuRef } = useContextMenu()  // 禁用右键菜单
</script>
```

## 🌟 最佳实践

1. **性能优化**: 光标使用transform而非left/top定位
2. **用户体验**: 提供清晰的视觉反馈
3. **可访问性**: 保持键盘导航支持
4. **兼容性**: 优雅降级到默认光标
5. **品牌一致**: 使用统一的颜色和动画

## 🔮 未来扩展

- 支持更多光标样式和动画效果
- 添加右键菜单的上下文感知功能
- 支持触摸设备的手势操作
- 添加光标轨迹效果
- 支持主题切换
