import { createRouter, createWebHistory } from 'vue-router'
import { dashboardRoutes } from './dashboard.js'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: () => import('../views/HomeView.vue'),
    },
    {
      path: '/account/login',
      name: 'login',
      component: () => import('../views/Account/Login/LoginView.vue'),
    },
    {
      path: '/account/login/register',
      name: 'register',
      component: () => import('../views/Account/Login/RegisterView.vue'),
    },
    // Dashboard 嵌套路由
    dashboardRoutes,
    // {
    //   path: '/about',
    //   name: 'about',
    //   // route level code-splitting
    //   // this generates a separate chunk (About.[hash].js) for this route
    //   // which is lazy-loaded when the route is visited.
    //   component: () => import('../views/AboutView.vue'),
    // },
  ],
})

export default router
