import { ref, onMounted, onUnmounted } from 'vue'

export function useContextMenu() {
  const contextMenuRef = ref(null)
  let savedSelection = null

  // 检测右键目标元素类型
  const detectMenuType = (target) => {
    // 检查是否是图片
    if (target.tagName === 'IMG') {
      return 'image'
    }

    // 检查是否是链接
    if (target.tagName === 'A' || target.closest('a')) {
      return 'link'
    }

    // 检查是否是输入框
    if (
      target.tagName === 'INPUT' ||
      target.tagName === 'TEXTAREA' ||
      target.contentEditable === 'true'
    ) {
      return 'input'
    }

    // 检查是否有选中文本
    if (window.getSelection().toString().trim()) {
      return 'text'
    }

    // 检查是否是文件相关元素
    if (target.classList.contains('file-item') || target.closest('.file-item')) {
      return 'file'
    }

    // 检查是否是侧边栏
    if (target.closest('.sidebar') || target.closest('.nav-item')) {
      return 'sidebar'
    }

    // 检查是否是表格
    if (target.closest('table') || target.closest('.el-table')) {
      return 'table'
    }

    // 默认通用菜单
    return 'default'
  }

  const handleClick = () => {
    // 隐藏右键菜单
    if (contextMenuRef.value) {
      contextMenuRef.value.hide()
    }
  }

  const handleContextMenu = (event) => {
    event.preventDefault()
    event.stopPropagation()

    // 在显示菜单前保存当前选中的文本
    const selection = window.getSelection()
    savedSelection = selection.toString().trim()

    // 检查右键的目标是否是输入框
    let rightClickedInput = null
    const target = event.target
    if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA') {
      rightClickedInput = target
    } else if (target.classList?.contains('el-input__inner')) {
      rightClickedInput = target
    } else {
      // 查找最近的输入框容器
      const inputContainer = target.closest('.el-input, .el-textarea')
      if (inputContainer) {
        rightClickedInput = inputContainer.querySelector('input, textarea')
      }
    }

    if (contextMenuRef.value) {
      const menuType = detectMenuType(event.target)
      // 传递右键点击的输入框信息
      contextMenuRef.value.show(event, menuType, rightClickedInput, savedSelection)
    }
  }

  const handleEscape = (event) => {
    if (event.key === 'Escape' && contextMenuRef.value) {
      contextMenuRef.value.hide()
    }
  }

  const init = () => {
    // 禁用默认右键菜单
    document.addEventListener('contextmenu', handleContextMenu)

    // 点击其他地方隐藏菜单
    document.addEventListener('click', handleClick)

    // ESC键隐藏菜单
    document.addEventListener('keydown', handleEscape)
  }

  const destroy = () => {
    document.removeEventListener('contextmenu', handleContextMenu)
    document.removeEventListener('click', handleClick)
    document.removeEventListener('keydown', handleEscape)
  }

  onMounted(() => {
    init()
  })

  onUnmounted(() => {
    destroy()
  })

  return {
    contextMenuRef,
    init,
    destroy,
  }
}
