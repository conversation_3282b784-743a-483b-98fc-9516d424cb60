<template>
  <div class="placeholder-page">
    <div class="page-header">
      <h1>消息中心</h1>
      <p>即时通讯功能正在开发中...</p>
    </div>

    <div class="coming-soon">
      <el-icon :size="64"><ChatLineRound /></el-icon>
      <h2>敬请期待</h2>
      <p>我们正在努力开发这个功能，很快就会与您见面！</p>
      <el-button type="primary" @click="goBack">返回工作台</el-button>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { ChatLineRound } from '@element-plus/icons-vue'

const router = useRouter()

const goBack = () => {
  router.push('/dashboard')
}
</script>

<style scoped lang="less">
@import '../../assets/styles/placeholder.less';
</style>
