<template>
  <div class="dashboard-home">
    <!-- 数据统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon">
          <el-icon><Message /></el-icon>
        </div>
        <div class="stat-info">
          <div class="stat-number">128</div>
          <div class="stat-label">未读消息</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">
          <el-icon><User /></el-icon>
        </div>
        <div class="stat-info">
          <div class="stat-number">56</div>
          <div class="stat-label">联系人</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">
          <el-icon><Shop /></el-icon>
        </div>
        <div class="stat-info">
          <div class="stat-number">12</div>
          <div class="stat-label">订单</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">
          <el-icon><Edit /></el-icon>
        </div>
        <div class="stat-info">
          <div class="stat-number">8</div>
          <div class="stat-label">博客文章</div>
        </div>
      </div>
    </div>

    <!-- 仪表盘小部件 -->
    <div class="dashboard-widgets">
      <div class="widget">
        <h3>最新动态</h3>
        <div class="activity-list">
          <div class="activity-item">
            <div class="activity-time">2分钟前</div>
            <div class="activity-content">张三给您发送了一条消息</div>
          </div>
          <div class="activity-item">
            <div class="activity-time">10分钟前</div>
            <div class="activity-content">您的博客文章收到了新评论</div>
          </div>
          <div class="activity-item">
            <div class="activity-time">1小时前</div>
            <div class="activity-content">商城订单已发货</div>
          </div>
        </div>
      </div>

      <div class="widget">
        <h3>快速访问</h3>
        <div class="quick-links">
          <router-link to="/dashboard/messages" class="quick-link">
            <el-icon><ChatLineRound /></el-icon>
            <span>查看消息</span>
          </router-link>
          <router-link to="/dashboard/blog" class="quick-link">
            <el-icon><Edit /></el-icon>
            <span>写博客</span>
          </router-link>
          <router-link to="/dashboard/shop" class="quick-link">
            <el-icon><Shop /></el-icon>
            <span>逛商城</span>
          </router-link>
          <router-link to="/dashboard/workshop" class="quick-link">
            <el-icon><Grid /></el-icon>
            <span>创意工坊</span>
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Message, User, Shop, Edit, ChatLineRound, Grid } from '@element-plus/icons-vue'
</script>

<style scoped lang="less">
@import '../assets/styles/common.less';

.dashboard-home {
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin-bottom: 32px;

    .stat-card {
      background: @white;
      border-radius: 12px;
      padding: 24px;
      border: 1px solid #f0f0f0;
      transition: all 0.2s ease;

      &:hover {
        border-color: #e5e7eb;
        transform: translateY(-1px);
      }

      .stat-icon {
        width: 56px;
        height: 56px;
        background: #f8fafc;
        border-radius: 14px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: @primary-color;
        margin-bottom: 20px;

        .el-icon {
          font-size: 24px;
        }
      }

      .stat-info {
        .stat-number {
          font-size: 32px;
          font-weight: 700;
          color: #1a1a1a;
          margin-bottom: 8px;
          letter-spacing: -1px;
        }

        .stat-label {
          font-size: 15px;
          color: #6b7280;
          font-weight: 500;
        }
      }
    }
  }

  .dashboard-widgets {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;

    .widget {
      background: @white;
      border-radius: 12px;
      padding: 24px;
      border: 1px solid #f0f0f0;

      h3 {
        font-size: 20px;
        font-weight: 600;
        color: #1a1a1a;
        margin-bottom: 24px;
        letter-spacing: -0.3px;
      }

      .activity-list {
        .activity-item {
          padding: 16px 0;
          border-bottom: 1px solid #f0f0f0;

          &:last-child {
            border-bottom: none;
          }

          .activity-time {
            font-size: 13px;
            color: #9ca3af;
            margin-bottom: 6px;
            font-weight: 500;
          }

          .activity-content {
            font-size: 15px;
            color: #374151;
            line-height: 1.5;
          }
        }
      }

      .quick-links {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 16px;

        .quick-link {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 12px;
          padding: 24px;
          background: #f8fafc;
          border-radius: 14px;
          text-decoration: none;
          transition: all 0.2s ease;
          border: 1px solid transparent;

          &:hover {
            background: #f0f9ff;
            border-color: #e0f2fe;
            transform: translateY(-1px);
          }

          .el-icon {
            font-size: 28px;
            color: @primary-color;
          }

          span {
            font-size: 15px;
            color: #374151;
            font-weight: 500;
          }
        }
      }
    }
  }
}
</style>
