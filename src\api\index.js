import axios from 'axios'
import envConfig from '../config/envconfig'

// 创建axios实例
const service = axios.create({
  baseURL: envConfig.baseURL,
  timeout: envConfig.timeout,
  headers: {
    'Content-Type': 'application/json;charset=utf-8',
  },
})

// 请求拦截器
service.interceptors.request.use(
  (config) => {
    // 在发送请求之前做些什么
    const token = localStorage.getItem('token')
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    // 对请求错误做些什么
    console.error('Request error:', error)
    return Promise.reject(error)
  },
)

// 响应拦截器
service.interceptors.response.use(
  (response) => {
    // 对响应数据做点什么
    const res = response.data

    // 自定义状态码判断
    if (res.code && res.code !== 200) {
      // 处理各种状态码
      if (res.code === 401) {
        // 未授权，清除token并重定向到登录页
        localStorage.removeItem('token')
        window.location.href = '/login'
      }

      return Promise.reject(new Error(res.message || 'Error'))
    } else {
      return res
    }
  },
  (error) => {
    // 对响应错误做点什么
    console.error('Response error:', error)

    // 处理网络错误
    const { response } = error
    if (response) {
      switch (response.status) {
        case 401:
          // 未授权，清除token并重定向到登录页
          localStorage.removeItem('token')
          window.location.href = '/login'
          break
        case 403:
          // 权限不足
          break
        case 404:
          // 请求的资源不存在
          break
        case 500:
          // 服务器错误
          break
        default:
          break
      }
    } else {
      // 请求超时或网络错误
      if (error.message.includes('timeout')) {
        console.error('请求超时!')
      } else {
        console.error('网络错误!')
      }
    }

    return Promise.reject(error)
  },
)

export default service
