@import './theme.less';

// 重置样式
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  font-family: @font-family;
  font-size: @font-size-base;
  color: @text-color;
  line-height: @line-height-base;
  background-color: @bg-color;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  text-decoration: none;
  color: @primary-color;
  transition: color @transition-duration;
  
  &:hover {
    color: @primary-light;
  }
}

button {
  cursor: pointer;
  border: none;
  outline: none;
  background: none;
  font-family: @font-family;
  transition: all @transition-duration;
}

// 辅助类
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 @spacing-lg;
}

// 响应式断点
@media (max-width: 768px) {
  .container {
    padding: 0 @spacing-md;
  }
}

// 渐变背景
.gradient-primary {
  background: linear-gradient(135deg, @primary-color, @accent-color);
}

// 阴影
.shadow {
  box-shadow: @box-shadow-base;
}

.shadow-lg {
  box-shadow: @box-shadow-darker;
}

// 圆角
.rounded {
  border-radius: @border-radius-base;
}

.rounded-lg {
  border-radius: @border-radius-lg;
}

// 通用过渡效果
.transition {
  transition: all @transition-duration;
} 