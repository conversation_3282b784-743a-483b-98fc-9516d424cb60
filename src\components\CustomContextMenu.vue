<template>
  <div v-if="visible" ref="menuRef" class="custom-context-menu" :style="menuStyle" @click.stop>
    <div class="menu-items">
      <!-- 渲染当前菜单项 -->
      <template v-for="(item, index) in currentMenuItems" :key="index">
        <div v-if="item.type === 'divider'" class="menu-divider"></div>
        <div v-else class="menu-item" @click="handleAction(item.action)">
          <el-icon><component :is="item.icon" /></el-icon>
          <span>{{ item.label }}</span>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Refresh,
  ArrowLeft,
  ArrowRight,
  Grid,
  CopyDocument,
  Share,
  DocumentCopy,
  Crop,
  Files,
  Edit,
  Delete,
  Star,
  Download,
  Setting,
} from '@element-plus/icons-vue'

const router = useRouter()

const visible = ref(false)
const x = ref(0)
const y = ref(0)
const menuRef = ref(null)
const currentMenuType = ref('default')

// 通用右键菜单配置
const menuConfigs = {
  // 默认通用菜单
  default: [
    { type: 'item', action: 'copy', label: '复制', icon: DocumentCopy },
    { type: 'item', action: 'paste', label: '粘贴', icon: Files },
    { type: 'divider' },
    { type: 'item', action: 'refresh', label: '刷新', icon: Refresh },
    { type: 'item', action: 'dashboard', label: '工作台', icon: Grid },
  ],

  // 文本选择菜单
  text: [
    { type: 'item', action: 'copy', label: '复制', icon: DocumentCopy },
    { type: 'item', action: 'cut', label: '剪切', icon: Crop },
    { type: 'item', action: 'paste', label: '粘贴', icon: Files },
    { type: 'divider' },
    { type: 'item', action: 'selectAll', label: '全选', icon: CopyDocument },
    // TODO: 添加更多文本操作功能
  ],

  // 图片右键菜单
  image: [
    { type: 'item', action: 'copyImage', label: '复制图片', icon: DocumentCopy },
    { type: 'item', action: 'saveImage', label: '保存图片', icon: Download },
    { type: 'item', action: 'copyImageUrl', label: '复制图片链接', icon: CopyDocument },
    { type: 'divider' },
    { type: 'item', action: 'openImageInNewTab', label: '在新标签页中打开', icon: ArrowRight },
    // TODO: 添加图片编辑、滤镜等功能
  ],

  // 链接右键菜单
  link: [
    { type: 'item', action: 'openLink', label: '打开链接', icon: ArrowRight },
    { type: 'item', action: 'openLinkInNewTab', label: '在新标签页中打开', icon: ArrowRight },
    { type: 'item', action: 'copyLink', label: '复制链接', icon: CopyDocument },
    { type: 'divider' },
    { type: 'item', action: 'bookmark', label: '添加到书签', icon: Star },
    // TODO: 添加链接预览、分享等功能
  ],

  // 文件/文档右键菜单
  file: [
    { type: 'item', action: 'openFile', label: '打开', icon: ArrowRight },
    { type: 'item', action: 'downloadFile', label: '下载', icon: Download },
    { type: 'item', action: 'copyFileName', label: '复制文件名', icon: DocumentCopy },
    { type: 'divider' },
    { type: 'item', action: 'shareFile', label: '分享', icon: Share },
    { type: 'item', action: 'deleteFile', label: '删除', icon: Delete },
    // TODO: 添加重命名、移动、属性等功能
  ],

  // 表单输入框右键菜单
  input: [
    { type: 'item', action: 'copy', label: '复制', icon: DocumentCopy },
    { type: 'item', action: 'cut', label: '剪切', icon: Crop },
    { type: 'item', action: 'paste', label: '粘贴', icon: Files },
    { type: 'divider' },
    { type: 'item', action: 'selectAll', label: '全选', icon: CopyDocument },
    { type: 'item', action: 'clear', label: '清空', icon: Delete },
    // TODO: 添加撤销、重做、格式化等功能
  ],

  // 侧边栏/导航菜单
  sidebar: [
    { type: 'item', action: 'refresh', label: '刷新', icon: Refresh },
    { type: 'item', action: 'collapse', label: '折叠侧边栏', icon: ArrowLeft },
    { type: 'divider' },
    { type: 'item', action: 'customize', label: '自定义布局', icon: Setting },
    // TODO: 添加拖拽排序、隐藏显示等功能
  ],

  // 数据表格右键菜单
  table: [
    { type: 'item', action: 'copyRow', label: '复制行', icon: DocumentCopy },
    { type: 'item', action: 'exportData', label: '导出数据', icon: Download },
    { type: 'divider' },
    { type: 'item', action: 'editRow', label: '编辑', icon: Edit },
    { type: 'item', action: 'deleteRow', label: '删除', icon: Delete },
    // TODO: 添加排序、筛选、统计等功能
  ],
}

// 计算当前菜单项
const currentMenuItems = computed(() => {
  return menuConfigs[currentMenuType.value] || menuConfigs.default
})

// 计算菜单位置样式，确保不超出屏幕
const menuStyle = computed(() => {
  return {
    left: x.value + 'px',
    top: y.value + 'px',
    transform: 'translate(0, 0)', // 默认位置
  }
})

// 智能定位菜单位置
const adjustMenuPosition = async () => {
  await nextTick()
  if (!menuRef.value) return

  const menu = menuRef.value
  const menuRect = menu.getBoundingClientRect()
  const viewportWidth = window.innerWidth
  const viewportHeight = window.innerHeight

  let adjustedX = x.value
  let adjustedY = y.value

  // 检查右边界
  if (x.value + menuRect.width > viewportWidth) {
    adjustedX = x.value - menuRect.width
  }

  // 检查下边界
  if (y.value + menuRect.height > viewportHeight) {
    adjustedY = y.value - menuRect.height
  }

  // 确保不超出左边界
  if (adjustedX < 0) {
    adjustedX = 10
  }

  // 确保不超出上边界
  if (adjustedY < 0) {
    adjustedY = 10
  }

  // 应用调整后的位置
  menu.style.left = adjustedX + 'px'
  menu.style.top = adjustedY + 'px'
}

const show = async (event, menuType = 'default', rightClickedInput = null, savedText = null) => {
  x.value = event.clientX
  y.value = event.clientY
  currentMenuType.value = menuType
  visible.value = true

  // 存储右键点击的输入框信息和保存的选中文本
  window.rightClickedInput = rightClickedInput
  window.savedSelectedText = savedText

  // 调整菜单位置
  await adjustMenuPosition()
}

const hide = () => {
  visible.value = false
  currentMenuType.value = 'default'
}

const handleAction = async (action) => {
  try {
    switch (action) {
      // 通用操作
      case 'copy':
        try {
          let textToCopy = ''

          // 优先使用保存的选中文本
          const savedText = window.savedSelectedText

          if (savedText && savedText.length > 0) {
            textToCopy = savedText
          } else {
            // 如果没有保存的文本，尝试获取当前选中的文本
            const selection = window.getSelection()
            const selectedText = selection.toString().trim()

            if (selectedText && selectedText.length > 0) {
              textToCopy = selectedText
            } else {
              // 检查输入框中是否有选中文本
              const activeElement = document.activeElement
              if (
                activeElement &&
                (activeElement.tagName === 'INPUT' || activeElement.tagName === 'TEXTAREA') &&
                activeElement.selectionStart !== activeElement.selectionEnd
              ) {
                textToCopy = activeElement.value.substring(
                  activeElement.selectionStart,
                  activeElement.selectionEnd,
                )
              } else {
                // 如果没有选中文本，复制页面URL
                textToCopy = window.location.href
              }
            }
          }

          // 使用最简单可靠的方法
          const textArea = document.createElement('textarea')
          textArea.value = textToCopy
          textArea.style.position = 'fixed'
          textArea.style.left = '-9999px'
          textArea.style.top = '-9999px'
          textArea.style.opacity = '0'
          document.body.appendChild(textArea)

          textArea.focus()
          textArea.select()

          let copySuccess = false
          try {
            copySuccess = document.execCommand('copy')
          } catch (execError) {
            // 静默处理错误
          }

          document.body.removeChild(textArea)

          // 如果传统方法失败，尝试现代API
          if (!copySuccess && navigator.clipboard && navigator.clipboard.writeText) {
            try {
              await navigator.clipboard.writeText(textToCopy)
              copySuccess = true
            } catch (clipboardError) {
              // 静默处理错误
            }
          }

          if (copySuccess) {
            ElMessage.success('复制成功')
          } else {
            ElMessage.error('复制失败')
          }
        } catch (error) {
          ElMessage.error('复制失败')
        }
        break

      case 'paste':
        try {
          const text = await navigator.clipboard.readText()

          if (!text) {
            ElMessage.warning('剪贴板为空')
            break
          }

          // 优先使用右键点击的输入框
          let targetElement = window.rightClickedInput

          // 如果没有右键点击输入框，则检查当前焦点元素
          if (!targetElement) {
            targetElement = document.activeElement

            // 如果焦点不在输入框上，则不执行粘贴
            if (
              !targetElement ||
              (targetElement.tagName !== 'INPUT' && targetElement.tagName !== 'TEXTAREA')
            ) {
              ElMessage.warning('请在输入框中右键粘贴')
              break
            }
          }

          // 确保目标元素是有效的输入框
          if (
            targetElement &&
            (targetElement.tagName === 'INPUT' || targetElement.tagName === 'TEXTAREA')
          ) {
            // 确保元素获得焦点
            targetElement.focus()

            // 获取当前光标位置
            const start = targetElement.selectionStart || 0
            const end = targetElement.selectionEnd || 0
            const currentValue = targetElement.value || ''

            // 在光标位置插入文本
            const newValue = currentValue.substring(0, start) + text + currentValue.substring(end)
            targetElement.value = newValue

            // 设置新的光标位置
            const newCursorPos = start + text.length
            targetElement.setSelectionRange(newCursorPos, newCursorPos)

            // 触发事件通知框架
            targetElement.dispatchEvent(new Event('input', { bubbles: true }))
            targetElement.dispatchEvent(new Event('change', { bubbles: true }))

            ElMessage.success('粘贴成功')
          } else {
            ElMessage.warning('请在输入框中右键粘贴')
          }
        } catch (error) {
          if (error.name === 'NotAllowedError') {
            ElMessage.error('没有剪贴板权限')
          } else {
            ElMessage.error('粘贴失败')
          }
        }
        break

      case 'refresh':
        window.location.reload()
        break

      case 'dashboard':
        router.push('/dashboard')
        break

      // 文本操作
      case 'cut':
        try {
          let textToCut = ''
          let cutSuccess = false

          // 优先检查输入框中的选中文本
          let targetElement = window.rightClickedInput || document.activeElement

          if (
            targetElement &&
            (targetElement.tagName === 'INPUT' || targetElement.tagName === 'TEXTAREA') &&
            targetElement.selectionStart !== targetElement.selectionEnd
          ) {
            // 输入框中选中文本的剪切
            const start = targetElement.selectionStart
            const end = targetElement.selectionEnd
            const value = targetElement.value
            textToCut = value.substring(start, end)

            if (textToCut) {
              await navigator.clipboard.writeText(textToCut)

              // 删除选中的文本
              const newValue = value.substring(0, start) + value.substring(end)
              targetElement.value = newValue

              // 设置光标位置
              targetElement.setSelectionRange(start, start)

              // 确保元素获得焦点
              targetElement.focus()

              // 触发事件 - 使用更多事件类型确保Vue检测到变化
              const inputEvent = new Event('input', { bubbles: true })
              const changeEvent = new Event('change', { bubbles: true })
              const keyupEvent = new Event('keyup', { bubbles: true })

              targetElement.dispatchEvent(inputEvent)
              targetElement.dispatchEvent(changeEvent)
              targetElement.dispatchEvent(keyupEvent)

              // 强制触发Vue的响应式更新
              if (targetElement._vei) {
                // Vue 3的事件处理
                Object.keys(targetElement._vei).forEach((key) => {
                  if (key.includes('input') || key.includes('change')) {
                    targetElement._vei[key](inputEvent)
                  }
                })
              }

              cutSuccess = true
            }
          } else {
            // 如果不是输入框剪切，则检查页面文本剪切
            const savedText = window.savedSelectedText

            if (savedText && savedText.length > 0) {
              // 页面选中文本的剪切 - 只复制，不删除
              textToCut = savedText
              await navigator.clipboard.writeText(textToCut)

              // 页面文本剪切后不需要删除，只需要复制到剪贴板
              cutSuccess = true
            }
          }

          if (cutSuccess) {
            ElMessage.success('剪切成功')
          } else {
            ElMessage.warning('请先选中要剪切的文本')
          }
        } catch (error) {
          ElMessage.error('剪切失败')
        }
        break

      case 'selectAll':
        activeElement = document.activeElement
        if (
          activeElement &&
          (activeElement.tagName === 'INPUT' || activeElement.tagName === 'TEXTAREA')
        ) {
          // 对于输入框，选中所有文本
          activeElement.select()
        } else {
          // 对于页面内容，选中所有文本
          const selection = window.getSelection()
          const range = document.createRange()
          range.selectNodeContents(document.body)
          selection.removeAllRanges()
          selection.addRange(range)
        }
        break

      case 'clear':
        const activeInput = document.activeElement
        if (
          activeInput &&
          (activeInput.tagName === 'INPUT' || activeInput.tagName === 'TEXTAREA')
        ) {
          activeInput.value = ''
          ElMessage.success('清空成功')
        }
        break

      // 图片操作
      case 'copyImage':
        // TODO: 实现图片复制功能
        ElMessage.info('图片复制功能开发中...')
        break

      case 'saveImage':
        // TODO: 实现图片保存功能
        ElMessage.info('图片保存功能开发中...')
        break

      case 'copyImageUrl':
        // TODO: 实现图片链接复制功能
        ElMessage.info('图片链接复制功能开发中...')
        break

      case 'openImageInNewTab':
        // TODO: 实现在新标签页打开图片
        ElMessage.info('新标签页打开功能开发中...')
        break

      // 链接操作
      case 'openLink':
        // TODO: 实现链接打开功能
        ElMessage.info('链接打开功能开发中...')
        break

      case 'openLinkInNewTab':
        // TODO: 实现在新标签页打开链接
        ElMessage.info('新标签页打开功能开发中...')
        break

      case 'copyLink':
        // TODO: 实现链接复制功能
        ElMessage.info('链接复制功能开发中...')
        break

      case 'bookmark':
        // TODO: 实现添加书签功能
        ElMessage.info('书签功能开发中...')
        break

      // 文件操作
      case 'openFile':
        // TODO: 实现文件打开功能
        ElMessage.info('文件打开功能开发中...')
        break

      case 'downloadFile':
        // TODO: 实现文件下载功能
        ElMessage.info('文件下载功能开发中...')
        break

      case 'copyFileName':
        // TODO: 实现文件名复制功能
        ElMessage.info('文件名复制功能开发中...')
        break

      case 'shareFile':
        // TODO: 实现文件分享功能
        ElMessage.info('文件分享功能开发中...')
        break

      case 'deleteFile':
        // TODO: 实现文件删除功能
        ElMessage.info('文件删除功能开发中...')
        break

      // 侧边栏操作
      case 'collapse':
        // TODO: 实现侧边栏折叠功能
        ElMessage.info('侧边栏折叠功能开发中...')
        break

      case 'customize':
        // TODO: 实现布局自定义功能
        ElMessage.info('布局自定义功能开发中...')
        break

      // 表格操作
      case 'copyRow':
        // TODO: 实现表格行复制功能
        ElMessage.info('表格行复制功能开发中...')
        break

      case 'exportData':
        // TODO: 实现数据导出功能
        ElMessage.info('数据导出功能开发中...')
        break

      case 'editRow':
        // TODO: 实现表格行编辑功能
        ElMessage.info('表格行编辑功能开发中...')
        break

      case 'deleteRow':
        // TODO: 实现表格行删除功能
        ElMessage.info('表格行删除功能开发中...')
        break

      default:
        console.warn(`未知的菜单操作: ${action}`)
        break
    }
  } catch (error) {
    console.error('菜单操作执行失败:', error)
    ElMessage.error('操作失败')
  }

  hide()
}

defineExpose({
  show,
  hide,
})
</script>

<style scoped lang="less">
.custom-context-menu {
  position: fixed;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border: 1px solid #f0f0f0;
  min-width: 200px;
  z-index: 10000;
  overflow: hidden;
  backdrop-filter: blur(20px);
  animation: contextMenuShow 0.2s ease;

  .menu-items {
    padding: 6px 0;

    .menu-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px 16px;
      cursor: pointer;
      transition: all 0.15s ease;
      color: #374151;
      font-size: 14px;
      font-weight: 500;

      &:hover {
        background: #f0f9ff;
        color: #2563eb;
      }

      &:active {
        background: #dbeafe;
      }

      .el-icon {
        font-size: 16px;
        width: 16px;
        height: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .menu-divider {
      height: 1px;
      background: #e5e7eb;
      margin: 6px 12px;
    }
  }
}

@keyframes contextMenuShow {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
</style>
