<template>
  <div class="dashboard-page">
    <!-- 顶部导航栏 -->
    <header class="dashboard-header">
      <div class="header-container">
        <!-- 左侧Logo和导航 -->
        <div class="header-left">
          <div class="logo">
            <h1>EliteHub</h1>
          </div>
          <nav class="main-navigation">
            <router-link
              to="/dashboard"
              class="nav-item"
              :class="{ active: $route.path === '/dashboard' }"
            >
              <el-icon><House /></el-icon>
              <span>工作台</span>
            </router-link>
            <router-link
              to="/dashboard/messages"
              class="nav-item"
              :class="{ active: $route.path === '/dashboard/messages' }"
            >
              <el-icon><ChatLineRound /></el-icon>
              <span>消息</span>
              <el-badge :value="unreadCount" :hidden="unreadCount === 0" class="nav-badge" />
            </router-link>
            <router-link
              to="/dashboard/contacts"
              class="nav-item"
              :class="{ active: $route.path === '/dashboard/contacts' }"
            >
              <el-icon><User /></el-icon>
              <span>联系人</span>
            </router-link>
            <router-link
              to="/dashboard/shop"
              class="nav-item"
              :class="{ active: $route.path === '/dashboard/shop' }"
            >
              <el-icon><Shop /></el-icon>
              <span>商城</span>
            </router-link>
            <router-link
              to="/dashboard/workshop"
              class="nav-item"
              :class="{ active: $route.path === '/dashboard/workshop' }"
            >
              <el-icon><Grid /></el-icon>
              <span>工坊</span>
            </router-link>
            <router-link
              to="/dashboard/blog"
              class="nav-item"
              :class="{ active: $route.path === '/dashboard/blog' }"
            >
              <el-icon><Edit /></el-icon>
              <span>博客</span>
            </router-link>
          </nav>
        </div>

        <!-- 右侧功能区 -->
        <div class="header-right">
          <!-- 搜索框 -->
          <div class="search-box">
            <el-input
              v-model="searchQuery"
              placeholder="搜索..."
              :prefix-icon="Search"
              class="search-input"
              clearable
            />
          </div>

          <!-- 通知 -->
          <div class="notification-btn" @click="showNotifications">
            <el-badge :value="notificationCount" :hidden="notificationCount === 0">
              <el-icon :size="20"><Bell /></el-icon>
            </el-badge>
          </div>

          <!-- 用户菜单 -->
          <el-dropdown class="user-dropdown" @command="handleUserCommand">
            <div class="user-avatar">
              <el-avatar :size="36" :src="userInfo.avatar">
                <el-icon><User /></el-icon>
              </el-avatar>
              <span class="username">{{ userInfo.name }}</span>
              <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">
                  <el-icon><User /></el-icon>
                  个人资料
                </el-dropdown-item>
                <el-dropdown-item command="settings">
                  <el-icon><Setting /></el-icon>
                  设置
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <el-icon><SwitchButton /></el-icon>
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="dashboard-main">
      <div class="main-container">
        <!-- 侧边栏 -->
        <aside class="sidebar" v-if="showSidebar">
          <div class="sidebar-content">
            <div class="quick-actions">
              <h3>{{ getSidebarTitle() }}</h3>
              <div class="action-buttons">
                <template v-for="action in getCurrentActions()" :key="action.key">
                  <el-button
                    :type="action.type"
                    :icon="action.icon"
                    @click="action.handler"
                    style="margin-left: 0"
                  >
                    {{ action.label }}
                  </el-button>
                </template>
              </div>
            </div>

            <div class="recent-items">
              <h3>{{ getRecentTitle() }}</h3>
              <div class="recent-list">
                <div class="recent-item" v-for="item in getCurrentRecentItems()" :key="item.id">
                  <el-icon><component :is="item.icon" /></el-icon>
                  <span>{{ item.name }}</span>
                </div>
              </div>
            </div>
          </div>
        </aside>

        <!-- 内容区域 -->
        <section class="content-area">
          <div class="content-header">
            <h2>{{ $route.meta.title || '工作台' }}</h2>
            <div class="content-actions">
              <el-button :icon="Refresh" circle @click="refreshContent" />
              <el-button :icon="FullScreen" circle @click="toggleFullscreen" />
            </div>
          </div>

          <div class="content-body">
            <!-- 子路由内容 -->
            <router-view />
          </div>
        </section>
      </div>
    </main>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  House,
  ChatLineRound,
  User,
  Shop,
  Grid,
  Edit,
  Search,
  Bell,
  ArrowDown,
  Setting,
  SwitchButton,
  Plus,
  Upload,
  Share,
  Document,
  Refresh,
  FullScreen,
  Message,
  FolderOpened,
  Star,
  Clock,
  Files,
  Camera,
  VideoCamera,
  Microphone,
} from '@element-plus/icons-vue'

// 响应式数据
const route = useRoute()
const router = useRouter()
const searchQuery = ref('')
const unreadCount = ref(5)
const notificationCount = ref(3)
const showSidebar = ref(true)

// 用户信息
const userInfo = ref({
  name: '用户名',
  avatar: '',
})

// 根据不同页面配置侧边栏内容
const sidebarConfig = {
  '/dashboard': {
    title: '快速操作',
    recentTitle: '最近访问',
    actions: [
      {
        key: 'create',
        label: '新建项目',
        type: 'primary',
        icon: Plus,
        handler: () => console.log('新建项目'),
      },
      {
        key: 'upload',
        label: '上传文件',
        type: 'default',
        icon: Upload,
        handler: () => console.log('上传文件'),
      },
      {
        key: 'share',
        label: '分享内容',
        type: 'default',
        icon: Share,
        handler: () => console.log('分享内容'),
      },
    ],
    recentItems: [
      { id: 1, name: '我的第一篇博客', icon: Edit },
      { id: 2, name: '项目文档', icon: Document },
      { id: 3, name: '设计稿', icon: Camera },
    ],
  },
  '/dashboard/messages': {
    title: '消息操作',
    recentTitle: '最近聊天',
    actions: [
      {
        key: 'newChat',
        label: '新建聊天',
        type: 'primary',
        icon: Plus,
        handler: () => console.log('新建聊天'),
      },
      {
        key: 'groupChat',
        label: '群聊',
        type: 'default',
        icon: User,
        handler: () => console.log('创建群聊'),
      },
      {
        key: 'voice',
        label: '语音通话',
        type: 'default',
        icon: Microphone,
        handler: () => console.log('语音通话'),
      },
    ],
    recentItems: [
      { id: 1, name: '张三', icon: User },
      { id: 2, name: '工作群', icon: User },
      { id: 3, name: '项目讨论', icon: User },
    ],
  },
  '/dashboard/contacts': {
    title: '联系人操作',
    recentTitle: '最近联系',
    actions: [
      {
        key: 'addContact',
        label: '添加好友',
        type: 'primary',
        icon: Plus,
        handler: () => console.log('添加好友'),
      },
      {
        key: 'createGroup',
        label: '创建群组',
        type: 'default',
        icon: User,
        handler: () => console.log('创建群组'),
      },
      {
        key: 'import',
        label: '导入联系人',
        type: 'default',
        icon: Upload,
        handler: () => console.log('导入联系人'),
      },
    ],
    recentItems: [
      { id: 1, name: '张三', icon: User },
      { id: 2, name: '李四', icon: User },
      { id: 3, name: '王五', icon: User },
    ],
  },
  '/dashboard/shop': {
    title: '商城操作',
    recentTitle: '最近浏览',
    actions: [
      {
        key: 'addProduct',
        label: '添加商品',
        type: 'primary',
        icon: Plus,
        handler: () => console.log('添加商品'),
      },
      {
        key: 'orders',
        label: '订单管理',
        type: 'default',
        icon: Document,
        handler: () => console.log('订单管理'),
      },
      {
        key: 'analytics',
        label: '数据分析',
        type: 'default',
        icon: Star,
        handler: () => console.log('数据分析'),
      },
    ],
    recentItems: [
      { id: 1, name: 'iPhone 15', icon: Shop },
      { id: 2, name: 'MacBook Pro', icon: Shop },
      { id: 3, name: 'AirPods', icon: Shop },
    ],
  },
  '/dashboard/workshop': {
    title: '工坊操作',
    recentTitle: '最近项目',
    actions: [
      {
        key: 'newProject',
        label: '新建项目',
        type: 'primary',
        icon: Plus,
        handler: () => console.log('新建项目'),
      },
      {
        key: 'templates',
        label: '模板库',
        type: 'default',
        icon: Grid,
        handler: () => console.log('模板库'),
      },
      {
        key: 'tools',
        label: '工具箱',
        type: 'default',
        icon: Setting,
        handler: () => console.log('工具箱'),
      },
    ],
    recentItems: [
      { id: 1, name: '网站设计', icon: Grid },
      { id: 2, name: 'Logo设计', icon: Camera },
      { id: 3, name: '视频剪辑', icon: VideoCamera },
    ],
  },
  '/dashboard/blog': {
    title: '博客操作',
    recentTitle: '最近文章',
    actions: [
      {
        key: 'newPost',
        label: '写文章',
        type: 'primary',
        icon: Edit,
        handler: () => console.log('写文章'),
      },
      {
        key: 'draft',
        label: '草稿箱',
        type: 'default',
        icon: Document,
        handler: () => console.log('草稿箱'),
      },
      {
        key: 'media',
        label: '媒体库',
        type: 'default',
        icon: FolderOpened,
        handler: () => console.log('媒体库'),
      },
    ],
    recentItems: [
      { id: 1, name: 'Vue3 学习笔记', icon: Edit },
      { id: 2, name: '前端开发心得', icon: Edit },
      { id: 3, name: '技术分享', icon: Edit },
    ],
  },
}

// 计算属性
const currentConfig = computed(() => {
  return sidebarConfig[route.path] || sidebarConfig['/dashboard']
})

// 方法
const getSidebarTitle = () => {
  return currentConfig.value.title
}

const getRecentTitle = () => {
  return currentConfig.value.recentTitle
}

const getCurrentActions = () => {
  return currentConfig.value.actions
}

const getCurrentRecentItems = () => {
  return currentConfig.value.recentItems
}

const handleUserCommand = (command) => {
  switch (command) {
    case 'profile':
      console.log('查看个人资料')
      break
    case 'settings':
      console.log('打开设置')
      break
    case 'logout':
      console.log('退出登录')
      break
  }
}

const showNotifications = () => {
  console.log('显示通知')
}

const createNew = () => {
  console.log('创建新内容')
}

const uploadFile = () => {
  console.log('上传文件')
}

const shareContent = () => {
  console.log('分享内容')
}

const refreshContent = () => {
  console.log('刷新内容')
}

const toggleFullscreen = () => {
  console.log('切换全屏')
}
</script>

<style scoped lang="less">
@import '../assets/styles/common.less';

.dashboard-page {
  min-height: 100vh;
  background: #fafbfc;
  display: flex;
  flex-direction: column;
}

// 顶部导航栏
.dashboard-header {
  background: #ffffff;
  border-bottom: 1px solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 1000;

  .header-container {
    max-width: 1600px;
    margin: 0 auto;
    padding: 0 32px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 72px;
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: 60px;

    .logo {
      h1 {
        color: #1a1a1a;
        font-size: 28px;
        font-weight: 700;
        margin: 0;
        letter-spacing: -0.5px;
      }
    }

    .main-navigation {
      display: flex;
      gap: 4px;

      .nav-item {
        display: flex;
        align-items: center;
        gap: 10px;
        padding: 12px 20px;
        border-radius: 10px;
        text-decoration: none;
        transition: all 0.2s ease;
        color: #6b7280;
        font-weight: 500;
        position: relative;
        font-size: 15px;

        &:hover {
          background: #f8fafc;
          color: #374151;
        }

        &.active {
          background: #f0f9ff;
          color: @primary-color;

          .nav-badge {
            :deep(.el-badge__content) {
              background: @primary-color;
              color: @white;
              border: 2px solid @white;
            }
          }
        }

        .nav-badge {
          position: absolute;
          top: 6px;
          right: 6px;
        }

        .el-icon {
          font-size: 18px;
        }
      }
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 16px;

    .search-box {
      .search-input {
        width: 320px;

        :deep(.el-input__wrapper) {
          background: #f8fafc;
          border-radius: 12px;
          border: 1px solid #e5e7eb;
          box-shadow: none;
          padding: 0 16px;
          height: 44px;

          &:hover {
            border-color: #d1d5db;
          }

          &.is-focus {
            border-color: @primary-color;
            background: @white;
          }
        }

        :deep(.el-input__inner) {
          font-size: 14px;
        }
      }
    }

    .notification-btn {
      width: 44px;
      height: 44px;
      border-radius: 12px;
      cursor: pointer;
      transition: all 0.2s ease;
      color: #6b7280;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        background: #f8fafc;
        color: #374151;
      }
    }

    .user-dropdown {
      // 去除 el-dropdown 默认的边框和轮廓
      outline: none;
      border: none;

      &:focus,
      &:focus-visible {
        outline: none;
        border: none;
        box-shadow: none;
      }

      .user-avatar {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 6px 16px 6px 6px;
        border-radius: 12px;
        cursor: pointer;
        transition: all 0.2s ease;
        outline: none;
        border: none;

        &:hover {
          background: #f8fafc;
        }

        &:focus,
        &:focus-visible {
          outline: none;
          border: none;
          box-shadow: none;
        }

        .username {
          font-weight: 500;
          color: #374151;
          font-size: 15px;
          color: @text-color;
        }

        .dropdown-icon {
          color: @text-color-secondary;
          font-size: 12px;
        }
      }
    }
  }
}

// 主要内容区域
.dashboard-main {
  flex: 1;
  display: flex;

  .main-container {
    max-width: 1600px;
    margin: 0 auto;
    padding: 24px;
    display: flex;
    gap: 24px;
    width: 100%;
  }

  .sidebar {
    width: 280px;
    background: @white;
    border-radius: 12px;
    padding: 24px;
    border: 1px solid #f0f0f0;
    height: fit-content;

    .quick-actions {
      margin-bottom: 32px;

      h3 {
        font-size: 16px;
        font-weight: 600;
        color: #1a1a1a;
        margin-bottom: 16px;
      }

      .action-buttons {
        display: flex;
        flex-direction: column;
        gap: 6px;

        .el-button {
          justify-content: flex-start;
          height: 44px;
          border-radius: 12px;
          font-weight: 500;
          font-size: 14px;
          background: transparent;
          border: none;
          color: #6b7280;
          transition: all 0.2s ease;
          padding: 0 16px;
          box-shadow: none;

          &:hover {
            background-color: #f8fafc;
            color: #374151;
            transform: translateY(-1px);
          }

          &:first-child {
            background-color: #3b82f6;
            color: #ffffff;
            font-weight: 600;

            &:hover {
              background-color: #2563eb;
              color: #ffffff;
              transform: translateY(-1px);
            }
          }

          &:focus {
            outline: none;
            box-shadow: none;
          }

          :deep(.el-icon) {
            margin-right: 10px;
            font-size: 16px;
          }

          :deep(.el-button__text-wrapper) {
            display: flex;
            align-items: center;
          }
        }
      }
    }

    .recent-items {
      h3 {
        font-size: 16px;
        font-weight: 600;
        color: #1a1a1a;
        margin-bottom: 16px;
      }

      .recent-list {
        .recent-item {
          display: flex;
          align-items: center;
          gap: 10px;
          padding: 8px 12px;
          border-radius: 8px;
          cursor: pointer;
          transition: all 0.2s ease;
          color: #6b7280;

          &:hover {
            background: #f8fafc;
            color: #374151;
          }

          .el-icon {
            font-size: 16px;
          }

          span {
            font-size: 14px;
            font-weight: 500;
          }
        }
      }
    }
  }

  .content-area {
    flex: 1;
    background: @white;
    border-radius: 16px;
    border: 1px solid #f0f0f0;
    overflow: hidden;

    .content-header {
      padding: 24px 32px;
      border-bottom: 1px solid #f0f0f0;
      display: flex;
      align-items: center;
      justify-content: space-between;

      h2 {
        font-size: 28px;
        font-weight: 600;
        color: #1a1a1a;
        margin: 0;
        letter-spacing: -0.5px;
      }

      .content-actions {
        display: flex;
        gap: 12px;

        .el-button {
          width: 44px;
          height: 44px;
          border-radius: 12px;
        }
      }
    }

    .content-body {
      padding: 32px;
    }
  }
}
</style>
