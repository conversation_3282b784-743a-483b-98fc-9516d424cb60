<template>
  <div class="auth-page">
    <div class="auth-container">
      <!-- 左侧品牌区域 -->
      <div class="brand-section">
        <div class="brand-content">
          <div class="logo">
            <h1>EliteHub</h1>
          </div>
          <h2>加入我们</h2>
          <p>开启您的数字化之旅</p>
          <div class="features-list">
            <div class="feature-item">
              <el-icon><Message /></el-icon>
              <span>即时通讯</span>
            </div>
            <div class="feature-item">
              <el-icon><Shop /></el-icon>
              <span>智慧商城</span>
            </div>
            <div class="feature-item">
              <el-icon><Edit /></el-icon>
              <span>创意工坊</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧注册表单 -->
      <div class="form-section">
        <div class="form-container">
          <div class="form-header">
            <h3>创建账户</h3>
            <p>已有账户？<router-link to="/login" class="link">立即登录</router-link></p>
          </div>

          <el-form
            ref="registerFormRef"
            :model="registerForm"
            :rules="registerRules"
            class="register-form"
            size="large"
          >
            <el-form-item prop="username">
              <el-input
                v-model="registerForm.username"
                placeholder="请输入用户名"
                prefix-icon="User"
                clearable
              />
            </el-form-item>

            <el-form-item prop="email">
              <el-input
                v-model="registerForm.email"
                placeholder="请输入邮箱"
                prefix-icon="Message"
                clearable
              />
            </el-form-item>

            <el-form-item prop="password">
              <el-input
                v-model="registerForm.password"
                type="password"
                placeholder="请输入密码"
                prefix-icon="Lock"
                show-password
                clearable
              />
            </el-form-item>

            <el-form-item prop="confirmPassword">
              <el-input
                v-model="registerForm.confirmPassword"
                type="password"
                placeholder="请确认密码"
                prefix-icon="Lock"
                show-password
                clearable
              />
            </el-form-item>

            <div class="form-options">
              <el-checkbox v-model="registerForm.agree">
                我已阅读并同意
                <el-link type="primary" class="terms-link">《用户协议》</el-link>
                和
                <el-link type="primary" class="terms-link">《隐私政策》</el-link>
              </el-checkbox>
            </div>

            <el-button
              type="primary"
              class="register-btn"
              :loading="loading"
              @click="handleRegister"
            >
              注册
            </el-button>
          </el-form>

          <div class="divider">
            <span>或</span>
          </div>

          <div class="social-login">
            <el-button class="social-btn wechat">
              <el-icon><ChatDotRound /></el-icon>
              微信注册
            </el-button>
            <el-button class="social-btn qq">
              <el-icon><User /></el-icon>
              QQ注册
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Message, Shop, Edit, ChatDotRound, User } from '@element-plus/icons-vue'

const router = useRouter()
const registerFormRef = ref()
const loading = ref(false)

// 注册表单数据
const registerForm = reactive({
  username: '',
  email: '',
  password: '',
  confirmPassword: '',
  agree: false
})

// 确认密码验证
const validateConfirmPassword = (rule, value, callback) => {
  if (value !== registerForm.password) {
    callback(new Error('两次输入的密码不一致'))
  } else {
    callback()
  }
}

// 表单验证规则
const registerRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 2, max: 20, message: '用户名长度在2到20个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    { validator: validateConfirmPassword, trigger: 'blur' }
  ]
}

// 处理注册
const handleRegister = async () => {
  if (!registerFormRef.value) return
  
  try {
    const valid = await registerFormRef.value.validate()
    if (!valid) return

    if (!registerForm.agree) {
      ElMessage.warning('请先同意用户协议和隐私政策')
      return
    }

    loading.value = true
    
    // 模拟注册请求
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success('注册成功！')
    router.push('/login')
  } catch (error) {
    console.error('注册失败:', error)
    ElMessage.error('注册失败，请稍后重试')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped lang="less">
@import '../assets/styles/common.less';

.auth-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: @spacing-lg;
}

.auth-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  max-width: 1000px;
  width: 100%;
  background: @white;
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  min-height: 700px;
}

// 左侧品牌区域
.brand-section {
  background: linear-gradient(135deg, @primary-color, @accent-color);
  color: @white;
  padding: @spacing-xxl;
  display: flex;
  align-items: center;
  justify-content: center;

  .brand-content {
    text-align: center;

    .logo h1 {
      font-size: 48px;
      font-weight: @font-weight-bold;
      margin-bottom: @spacing-lg;
      color: @white;
    }

    h2 {
      font-size: 32px;
      font-weight: @font-weight-bold;
      margin-bottom: @spacing-md;
    }

    p {
      font-size: @font-size-lg;
      opacity: 0.9;
      margin-bottom: @spacing-xxl;
    }

    .features-list {
      display: flex;
      flex-direction: column;
      gap: @spacing-lg;

      .feature-item {
        display: flex;
        align-items: center;
        gap: @spacing-md;
        font-size: @font-size-lg;

        .el-icon {
          font-size: 24px;
        }
      }
    }
  }
}

// 右侧表单区域
.form-section {
  padding: @spacing-xxl;
  display: flex;
  align-items: center;
  justify-content: center;

  .form-container {
    width: 100%;
    max-width: 400px;

    .form-header {
      text-align: center;
      margin-bottom: @spacing-xl;

      h3 {
        font-size: 28px;
        font-weight: @font-weight-bold;
        color: @text-color;
        margin-bottom: @spacing-sm;
      }

      p {
        color: @text-color-secondary;
        font-size: @font-size-base;

        .link {
          color: @primary-color;
          text-decoration: none;
          font-weight: @font-weight-medium;

          &:hover {
            text-decoration: underline;
          }
        }
      }
    }

    .register-form {
      margin-bottom: @spacing-lg;

      .form-options {
        margin-bottom: @spacing-xl;

        .terms-link {
          font-size: @font-size-sm;
        }
      }

      .register-btn {
        width: 100%;
        height: 48px;
        background: linear-gradient(135deg, @primary-color, @accent-color);
        border: none;
        border-radius: 12px;
        font-size: @font-size-lg;
        font-weight: @font-weight-medium;
        box-shadow: 0 4px 12px rgba(58, 123, 213, 0.3);

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 20px rgba(58, 123, 213, 0.4);
        }
      }
    }

    .divider {
      text-align: center;
      margin: @spacing-lg 0;
      position: relative;

      &::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        height: 1px;
        background: @border-color-light;
      }

      span {
        background: @white;
        padding: 0 @spacing-md;
        color: @text-color-secondary;
        font-size: @font-size-sm;
      }
    }

    .social-login {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: @spacing-md;

      .social-btn {
        height: 48px;
        border-radius: 12px;
        font-weight: @font-weight-medium;

        &.wechat {
          background: #07c160;
          border-color: #07c160;
          color: @white;

          &:hover {
            background: #06ad56;
          }
        }

        &.qq {
          background: #12b7f5;
          border-color: #12b7f5;
          color: @white;

          &:hover {
            background: #0ea5e9;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .auth-container {
    grid-template-columns: 1fr;
    max-width: 400px;
    margin: @spacing-lg;

    .brand-section {
      padding: @spacing-xl @spacing-lg;

      .brand-content {
        .logo h1 {
          font-size: 36px;
        }

        h2 {
          font-size: 24px;
        }

        .features-list {
          flex-direction: row;
          justify-content: center;
          flex-wrap: wrap;
        }
      }
    }

    .form-section {
      padding: @spacing-xl @spacing-lg;
    }
  }
}
</style>
